import { createApp } from 'vue';
import mockjs from 'mockjs?url';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import tzFormat from 'dayjs-tz-format';
import App from './App.vue';
import pinia from './stores';
import { loadScriptSync } from '@/utils';
// import 'virtual:uno.css';
import './styles/app.less';
// Vant 桌面端适配
// import '@vant/touch-emulator';

dayjs.extend(timezone);
dayjs.extend(utc);
dayjs.tz.setDefault('Asia/Shanghai');
dayjs.extend(tzFormat);

const app = createApp(App);

app.use(pinia);

const main = async () => {
    try {
        const { mock } = myWebview.params;
        if (mock)
            await loadScriptSync(mockjs);
    }
    catch {}
    app.mount('#app');
};

main();
