import { defineStore } from 'pinia';
import { getRoomResourceInfo as getRoomResourceInfoApi } from '@/pendant/api';

const usePendantInitStore = defineStore('pendant-init', () => {
    const state = reactive({
        initData: {},
        pendantServerTime: 0,
    });

    const inited = ref(false);

    const isEnd = computed(() => {
        const { endTime } = state.initData;
        return state.pendantServerTime >= endTime;
    });

    let countTimeKey = null;
    function countServerTime(pendantServerTime) {
        if (countTimeKey) {
            clearInterval(countTimeKey);
            countTimeKey = null;
        }
        window.pendantServerTime = pendantServerTime;
        state.pendantServerTime = pendantServerTime;
        countTimeKey = setInterval(() => {
            window.pendantServerTime += 1;
            state.pendantServerTime += 1;
        }, 1000);
    }

    const getRoomResourceInfo = async (payload = {}, config = {}) => {
        const [res] = await getRoomResourceInfoApi(
            // { params: JSON.stringify({ room: { uid: 0 } }) }
            { params: [JSON.stringify({ getLuckyCards: { ...payload, uid: myWebview.params.uid } })] },
            config,
        );
        if (res?.code === 0) {
            let data = {};
            try {
                data = JSON.parse(res?.data?.result || '{}');
            }
            catch (error) {
                data = {};
            }
            // console.log(data);
            state.initData = {
                //
            };
            inited.value = true;
            if (data.serverTime) {
                countServerTime(data.serverTime);
            }
        }
    };

    return {
        ...toRefs(state),
        inited,
        isEnd,
        getRoomResourceInfo,
    };
});

export default usePendantInitStore;
