import axios from 'axios';
import { getToken } from '@/utils/jsbridge';
import { env, nodeUrl } from '@/config/url';
import config from '@/config';

export const createAxios = (axiosConfig = {}) => {
    const request = axios.create({
        baseURL: nodeUrl({ nodePath: config.pendantNodePath, key: 'node_common_' }),
        timeout: 10000, // 请求超时时间
        ...axiosConfig,
    });

    // 请求拦截器
    request.interceptors.request.use(
        (conf) => {
            // 添加token
            const token = getToken() || undefined;
            if (conf.method === 'get')
                conf.params = Object.assign(
                    { token, uid: myWebview.params.uid || undefined },
                    conf.params,
                );
            else if (conf.method === 'post')
                conf.data = Object.assign(
                    { token, uid: myWebview.params.uid || undefined },
                    conf.data,
                );

            if (token)
                conf.headers.Authorization = token;

            if (env === 'gray' || myWebview.params?.requestGray === 1)
                // 灰度环境 或者 参数强制，添加灰度标识
                conf.headers['x-qw-traffic-mark'] = 'staging';
            return conf;
        },
        (error) => {
            return Promise.reject(error);
        },
    );

    // 异常拦截处理器
    request.interceptors.response.use(
        (response) => {
            const data = response.data;
            return data;
        },
        (error) => {
            return Promise.reject(error);
        },
    );

    return request;
};

export default createAxios();
