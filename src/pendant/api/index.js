// 执行pb-p.cjs命令后会覆盖掉手写的代码，请谨慎操作!
import request from './request';
import getMockData from './mockData';
import { env } from '@/config/url';

export const fetchApi = ({
    proPrefix = '/room.Room/',
    api,
    data = {},
    config = {},
}) => {
    const { mock } = myWebview.params;
    if (mock && env !== 'prod') {
        return to(getMockData(api, data));
    }
    const url = `${proPrefix}/${api}`.replace('//', '/');
    return to(request.post(url, data, config));
};

const REQUEST_API_MAP = {
    getRoomResourceInfo: 'getRoomResourceInfo',
};

export const getRoomResourceInfo = (data, config) => fetchApi({ api: REQUEST_API_MAP.getRoomResourceInfo, data, config });

// batchGetRoomResourceInfo, api: getRoomResourceInfo
// params: [JSON.stringify({ hook1: params })] : [JSON.stringify({ hook2: params })]
