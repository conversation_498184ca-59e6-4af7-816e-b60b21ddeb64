import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import tzFormat from 'dayjs-tz-format';

export const TIME_ZONE = 'Asia/Shanghai';

dayjs.extend(timezone);
dayjs.extend(utc);
dayjs.tz.setDefault(TIME_ZONE);
dayjs.extend(tzFormat);

// 北京时间2025/03/13 00:00:00这一刻的时间戳，等同于澳洲时间2025/03/13 02:00:00这一刻的时间戳
// export const XXX_TIME_STAMP = getDateTextDetails('2025/03/13 00:00:00').timeStamp;

export function getNow() {
    return window.pendantServerTime || dayjs().unix();
}

export function getNowDayjsDate() {
    return dayjs.tz(getNow() * 1000, TIME_ZONE);
}

// 东8区(北京时间) 详情信息
export function getBeijingDatejsDateDetails(dayjsDate) {
    const year = dayjsDate.year();
    const yearText = `${year}`;
    const month = dayjsDate.month() + 1;
    const monthText = `00${month}`.slice(-2);
    const day = dayjsDate.date();
    const dayText = `00${day}`.slice(-2);
    const hour = dayjsDate.hour();
    const hourText = `00${hour}`.slice(-2);
    const minute = dayjsDate.minute();
    const minuteText = `00${minute}`.slice(-2);
    const second = dayjsDate.second();
    const secondText = `00${second}`.slice(-2);
    const dateStartTimeStamp = dayjsDate.startOf('day').unix();
    const dateEndTimeStamp = dayjsDate.endOf('day').unix();
    const dateStartTimeText = `${yearText}/${monthText}/${dayText} 00:00:00`;
    const dateEndTimeText = `${yearText}/${monthText}/${dayText} 23:59:59`;

    return {
        year,
        yearText,
        month,
        monthText,
        day,
        dayText,
        hour,
        hourText,
        minute,
        minuteText,
        second,
        secondText,
        dateText: `${yearText}${monthText}${dayText}`,
        fullTextPrev: `${yearText}/${monthText}/${dayText}`,
        fullText: `${yearText}/${monthText}/${dayText} ${hourText}:${minuteText}:${secondText}`,
        dateStartTimeStamp,
        dateEndTimeStamp,
        dateStartTimeText,
        dateEndTimeText,
    };
}

// 东8区(北京时间) 时间戳 详情信息
export function getTimeStampDetails(timeStamp) {
    const datejsDate = dayjs.tz(timeStamp * 1000, TIME_ZONE);
    return { ...getBeijingDatejsDateDetails(datejsDate), timeStamp: datejsDate.unix() };
}

// 东8区(北京时间) 时间文本 详情信息 2025/04/17 00:00:00
export function getDateTextDetails(fullDateText) {
    const datejsDate = dayjs.tz(fullDateText, TIME_ZONE);
    return { ...getBeijingDatejsDateDetails(datejsDate), timeStamp: datejsDate.unix() };
}

// 东8区(北京时间) 时间文本 转换为 时间戳
export function getDateTextTimeStamp(dateText) {
    return dayjs.tz(dateText, TIME_ZONE).unix();
}

// 东8区(北京时间) 当前时间信息
export function getCurrentTimeDetails() {
    return getBeijingDatejsDateDetails(
        window.pendantServerTime
            ? dayjs.tz(window.pendantServerTime * 1000, TIME_ZONE)
            : dayjs().tz(TIME_ZONE),
    );
}

// 东8区(北京时间) 活动时间内每日某时间段时间详情信息 [`7:00:00`, `9:00:00`]
export function getEveryDayRangeDetails(range) {
    if (window.activityStartTimeStamp && window.activityEndTimeStamp) {
        const detailsArr = [];
        let time = window.activityStartTimeStamp;
        while (time <= window.activityEndTimeStamp) {
            const { yearText, monthText, dayText } = getTimeStampDetails(time);
            const dateText = `${yearText}/${monthText}/${dayText}`;
            const start = dayjs.tz(`${dateText} ${range[0]}`, TIME_ZONE).unix();
            const end = dayjs.tz(`${dateText} ${range[1]}`, TIME_ZONE).unix();
            detailsArr.push({
                start,
                end,
                startDetails: getTimeStampDetails(start),
                endDetails: getTimeStampDetails(end),
            });
            time += 86400;
        }
        return detailsArr;
    }
    return [];
}

// 东8区(北京时间) 今日某时间段时间详情信息 [`7:00:00`, `9:00:00`]
export function getTodayRangeDetails(range) {
    const todayDayjsDate = getNowDayjsDate().millisecond(0);
    const [startHour, startMin, startSec] = range[0].split(':');
    const [endHour, endMin, endSec] = range[1].split(':');
    const startDate = todayDayjsDate.hour(startHour).minute(startMin).second(startSec);
    let endDate = todayDayjsDate.hour(endHour).minute(endMin).second(endSec);
    if (endDate.isBefore(startDate)) {
        endDate = endDate.add(1, 'day');
    }
    const start = Number.parseInt(startDate.valueOf() / 1000);
    const end = Number.parseInt(endDate.valueOf() / 1000);
    return {
        originStartText: range[0],
        originEndText: range[1],
        startHour,
        startMin,
        startSec,
        endHour,
        endMin,
        endSec,
        start,
        end,
    };
}

// 参数时间为多久前
export function getTimeDistance(timestamp) {
    const now = getNow();
    const less = now - timestamp;
    if (less <= 0) {
        return '刚刚';
    }
    if (less < 60) {
        return `${less}秒前`;
    }
    else if (less < 3600) {
        return `${Number.parseInt(less / 60, 10)}分钟前`;
    }
    else if (less < 86400) {
        return `${Number.parseInt(less / 3600, 10)}小时前`;
    }
    else {
        return `${Number.parseInt(less / 86400, 10)}天前`;
    }
}
