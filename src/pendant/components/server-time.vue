<template>
    <div v-show="initStore.pendantServerTime" class="server-time">{{ currentServerTime }}</div>
</template>

<script setup>
import usePendantInitStore from '../stores/modules/use-pendant-init-store';
import { getTimeStampDetails } from '@/utils/time';

const initStore = usePendantInitStore();

const currentServerTime = computed(() => {
    const { pendantServerTime } = initStore;
    if (!pendantServerTime) {
        return '暂无服务器时间';
    }
    return getTimeStampDetails(pendantServerTime).fullText.replace(/\//g, '-');
});
</script>

<style lang="less" scoped>
.server-time {
    position: fixed;
    top: 0;
    left: 0;
    width: 375px;
    line-height: 30px;
    z-index: 99999;
    text-align: center;
    color: white;
    font-size: 16px;
    background-color: rgba(0, 0, 0, 0.3);
    pointer-events: none;
}
</style>
