/**
 * @description 目前有tt(tt语音),hy(欢游),mk(麦可),mj(谜境)4个马甲包配置
 * @description ios，android表示当前系统，tt其实是统一，但是hy跟mk需要
 * @param api: 后台服务地址
 * @param node: 活动的node服务地址
 * @param node_common: 公共的node服务地址（短链、口令、微信签名等）
 * @param web: 活动页面地址
 * @param activeAppOptions: 唤醒包的配置表
 * @param jsBridgeProtocol: 跟客户端对接的JSBridge协议头
 * @param jsBridgePreview: 跟客户端对接的JSBridge协议头+域名
 * @param website: 对应当前马甲包的官网
 * @param app: 版本页面地址
 * @param platformApp app马甲分享图
 */

export default {
    app_name: '麦可',
    face_ios: {
        prod: '//api.52tt.com',
        gray: '//gray.52tt.com',
        testing: '//testing-api.ttyuyin.com',
        dev: '//testing-api.ttyuyin.com',
    },
    face_android: {
        prod: '//api.52tt.com',
        gray: '//gray.52tt.com',
        testing: '//testing-api.ttyuyin.com',
        dev: '//testing-api.ttyuyin.com',
    },
    api_ios: {
        prod: '//api.tingyou.fun/',
        gray: '//api.tingyou.fun/',
        testing: '//testing-tt-web-tc.ttyuyin.com/',
        dev: '//testing-tt-web-tc.ttyuyin.com/',
    },
    api_android: {
        prod: '//api.yuyue27.com/',
        gray: '//api.yuyue27.com/',
        testing: '//testing-tt-web-tc.ttyuyin.com/',
        dev: '//testing-tt-web-tc.ttyuyin.com/',
    },
    node_ios: {
        prod: '//node-unify.tingyou.fun/activity-production/',
        gray: '//node-unify.tingyou.fun/activity-testing/',
        testing: '//testing-tt-web-tc.ttyuyin.com/activity-testing/',
        dev: '//testing-tt-web-tc.ttyuyin.com/activity-testing/',
    },
    node_android: {
        prod: '//node-unify.yuyue27.com/activity-production/',
        gray: '//node-unify.yuyue27.com/activity-testing/',
        testing: '//testing-tt-web-tc.ttyuyin.com/activity-testing/',
        dev: '//testing-tt-web-tc.ttyuyin.com/activity-testing/',
    },
    node_room_ios: {
        prod: '//node-room.52tt.com/common-production/',
        gray: '//node-room.52tt.com/common-production/',
        testing: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
        dev: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
    },
    node_room_android: {
        prod: '//node-room.52tt.com/common-production/',
        gray: '//node-room.52tt.com/common-production/',
        testing: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
        dev: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
    },
    node_common_ios: {
        prod: '//node-unify.tingyou.fun/common-production/',
        gray: '//node-unify.tingyou.fun/common-production/',
        testing: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
        dev: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
    },
    node_common_android: {
        prod: '//node-unify.yuyue27.com/common-production/',
        gray: '//node-unify.yuyue27.com/common-production/',
        testing: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
        dev: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
    },
    avatar_ios: {
        prod: 'https://avatar.tingyou.fun/v2/',
        gray: 'https://avatar.tingyou.fun/v2/',
        testing: 'https://testing-avatar.ttyuyin.com/v2/',
        dev: 'https://testing-avatar.ttyuyin.com/v2/',
    },
    avatar_android: {
        prod: 'https://avatar.yuyue27.com/v2/',
        gray: 'https://avatar.yuyue27.com/v2/',
        testing: 'https://testing-avatar.ttyuyin.com/v2/',
        dev: 'https://testing-avatar.ttyuyin.com/v2/',
    },
    web_ios: {
        dev: 'https://app.tingyou.fun/testing/frontend-web-activity-',
        internal: 'https://app.tingyou.fun/internal/frontend-web-activity-',
        testing: 'https://app.tingyou.fun/testing/frontend-web-activity-',
        gray: 'https://app.tingyou.fun/gray/frontend-web-activity-',
        prod: 'https://appcdn.tingyou.fun/web/frontend-web-activity-',
    },
    web_ios_share: {
        dev: 'https://app.tingyou.fun/testing/frontend-web-activity-',
        internal: 'https://app.tingyou.fun/internal/frontend-web-activity-',
        testing: 'https://app.tingyou.fun/testing/frontend-web-activity-',
        gray: 'https://app.tingyou.fun/gray/frontend-web-activity-',
        prod: 'https://cdn.tingyou.fun/web/frontend-web-activity-',
    },
    web_android: {
        dev: 'https://app.yuyue27.com/testing/frontend-web-activity-',
        internal: 'https://app.yuyue27.com/internal/frontend-web-activity-',
        testing: 'https://app.yuyue27.com/testing/frontend-web-activity-',
        gray: 'https://app.yuyue27.com/gray/frontend-web-activity-',
        prod: 'https://appcdn.yuyue27.com/web/frontend-web-activity-',
    },
    web_android_share: {
        dev: 'https://app.yuyue27.com/testing/frontend-web-activity-',
        internal: 'https://app.yuyue27.com/internal/frontend-web-activity-',
        testing: 'https://app.yuyue27.com/testing/frontend-web-activity-',
        gray: 'https://app.yuyue27.com/gray/frontend-web-activity-',
        prod: 'https://cdn.yuyue27.com/web/frontend-web-activity-',
    },
    web_ios_host: {
        dev: 'https://app.tingyou.fun',
        internal: 'https://app.tingyou.fun',
        testing: 'https://app.tingyou.fun',
        gray: 'https://app.tingyou.fun',
        prod: 'https://appcdn.tingyou.fun',
    },
    web_ios_share_host: {
        dev: 'https://app.tingyou.fun',
        internal: 'https://app.tingyou.fun',
        testing: 'https://app.tingyou.fun',
        gray: 'https://app.tingyou.fun',
        prod: 'https://cdn.tingyou.fun',
    },
    web_android_host: {
        dev: 'https://app.yuyue27.com',
        internal: 'https://app.yuyue27.com',
        testing: 'https://app.yuyue27.com',
        gray: 'https://app.yuyue27.com',
        prod: 'https://appcdn.yuyue27.com',
    },
    web_android_share_host: {
        dev: 'https://app.yuyue27.com',
        internal: 'https://app.yuyue27.com',
        testing: 'https://app.yuyue27.com',
        gray: 'https://app.yuyue27.com',
        prod: 'https://cdn.yuyue27.com',
    },
    app_ios: {
        dev: 'https://app.tingyou.fun/testing/frontend-web-assist-',
        internal: 'https://app.tingyou.fun/internal/frontend-web-assist-',
        testing: 'https://app.tingyou.fun/testing/frontend-web-assist-',
        gray: 'https://app.tingyou.fun/gray/frontend-web-assist-',
        prod: 'https://appcdn.tingyou.fun/web/frontend-web-assist-',
    },
    app_android: {
        dev: 'https://app.yuyue27.com/testing/frontend-web-assist-',
        internal: 'https://app.yuyue27.com/internal/frontend-web-assist-',
        testing: 'https://app.yuyue27.com/testing/frontend-web-assist-',
        gray: 'https://app.yuyue27.com/gray/frontend-web-assist-',
        prod: 'https://appcdn.yuyue27.com/web/frontend-web-assist-',
    },
    activeAppOptions: {
        androidLink: 'm://yuyue27.com/',
        universal: 'https://ul.tingyou.fun',
        appstore: 'https://itunes.apple.com/cn/app/id1619035018',
        fallback: 'http://a.app.qq.com/o/simple.jsp?pkgname=com.yuyue.mic',
        official: 'https://d.yuyue27.com/maike/official/maike.apk',
        createPsw: true,
        marketId: 5,
    },
    jsBridgeProtocol: 'ymic://',
    jsBridgePreview: 'ymic://m.tingyou.fun',
    website_ios: 'https://web.tingyou.fun',
    website_android: 'https://yuyue27.com',
    platformApp: {
        type: 'TT',
        img: requireImg('share/mk_no_compress.png'),
        txt: '麦可好友',
        ext: 'tt_playmate_click',
    },
};
