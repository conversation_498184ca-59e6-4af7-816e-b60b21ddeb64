import tt from './vests/tt';
import hy from './vests/hy';
import mk from './vests/mk';
import mj from './vests/mj';
import ttBeluga from './vests-beluga/tt';
import hyBeluga from './vests-beluga/hy';
import mkBeluga from './vests-beluga/mk';
import mjBeluga from './vests-beluga/mj';

// 导出当前马甲的配置信息
const getUrlConfig = () => {
    switch (MARKET_ID) {
        case MARKET_ID_MAP.TT:
            return tt;
        case MARKET_ID_MAP.HY:
            return hy;
        case MARKET_ID_MAP.MK:
            return mk;
        case MARKET_ID_MAP.MJ:
            return mj;

        default:
            return tt;
    }
};

export const getBelugaUrlConfig = () => {
    switch (MARKET_ID) {
        case MARKET_ID_MAP.TT:
            return ttBeluga;
        case MARKET_ID_MAP.HY:
            return hyBeluga;
        case MARKET_ID_MAP.MK:
            return mkBeluga;
        case MARKET_ID_MAP.MJ:
            return mjBeluga;
        default:
            return ttBeluga;
    }
};

export const urlConfig = getUrlConfig();
export const belugaUrlConfig = getBelugaUrlConfig();
