import { urlConfig } from './url-config';
import config from './index';

/**
 * @name getENV 根据URL获取当前环境
 *  返回当前环境 ['prod', 'gray', 'testing', 'dev']
 */
const getENV = () => {
    const urlString = window.location.href;
    const prod = [
        '.com/web/',
        '.net/web/',
        '.com/project/',
        '.net/project/',
        '.fun/web/',
        '.com/newweb',
        '.net/newweb',
        '.fun/newweb',
    ];
    const gray = ['.com/gray/', '.net/gray/', '.fun/gray/'];
    const testing = ['.com/testing/', '.net/testing/', '.fun/testing/'];

    const checkIsMixed = (a, b) => a.some(v => b.includes(v));

    if (checkIsMixed(prod, urlString))
        return 'prod';
    else if (checkIsMixed(gray, urlString))
        return 'gray';
    else if (checkIsMixed(testing, urlString))
        return 'testing';
    else return 'dev';
};

export const env = getENV();

const osType = myWebview.isIOS() ? 'ios' : 'android';

// 导出node服务域名
export const nodeUrl = ({ nodePath = config.nodePath, key = 'node_' } = {}) => {
    const { shareMock, localNode } = myWebview.params;
    // 指向共享平台的mock服务
    if (shareMock)
        return `http://localhost:3002`;
    // 开发环境，指向特定的地址
    if (env === 'dev') {
        if (localNode) {
            return 'http://*************:8080';
        }
        else {
            return `https:${urlConfig[`${key}${osType}`].testing}${nodePath}`;
        }
    }
    return `${urlConfig[`${key}${osType}`][env]}${nodePath}`;
};

// 导出后端域名
export const apiUrl = `${urlConfig[`api_${osType}`][env]}`;

// 导出face人脸域名
export const faceUrl = `${urlConfig[`face_${osType}`][env]}`;

// 导出当前页面域名
export const curUrl = `${window.location.protocol}//${window.location.host}${window.location.pathname}`;

// 导出当前环境对应的页面域名
export const webUrl = `${urlConfig[`web_${osType}`][env]}${config.projectName}/index.html`;
export const webUrlShare = `${urlConfig[`web_${osType}_share`][env]}${config.projectName}/index.html`;

// 版本页面地址
export const assistUrl = `${urlConfig[`app_${osType}`][env]}`;

// 导出头像地址
export const avatarUrl = urlConfig[`avatar_${osType}`][env];

// 短链协议 tt://
export const jsBridgeProtocol = urlConfig.jsBridgeProtocol;

// 完整短链协议+域名 tt://m.52tt.com
export const jsBridgePreview = urlConfig.jsBridgePreview;

// 官网地址
export const website = urlConfig[`website_${osType}`];

// 唤起app参数
export const activeAppOptions = urlConfig.activeAppOptions;

// obs接口地址
export const obsDomain = 'https://obs.52tt.com/object/';

// web页面域名集合
export const ALL_HOST = [
    'http://app.52tt.com',
    'https://appcdn.52tt.com',
    'http://app.i52tt.com',
    'https://cdn.i52tt.com',
    'https://app.tses.net',
    'https://appcdn.tses.net',
    'https://cdn.tses.net',
    'https://zy-app.rzhushou.com',
    'https://zy-appcdn.rzhushou.com',
    'https://zy-cdn.rzhushou.com',
    'https://app.tingyou.fun',
    'https://appcdn.tingyou.fun',
    'https://cdn.tingyou.fun',
    'https://app.yuyue27.com',
    'https://appcdn.yuyue27.com',
    'https://cdn.yuyue27.com',
    'https://app.ukilive.com',
    'https://appcdn.ukilive.com',
    'https://cdn.ukilive.com',
];

// web页面域名集合(端外分享)
export const SHARE_HOST = [
    'http://app.i52tt.com',
    'https://cdn.i52tt.com',
    'https://cdn.tses.net',
    'https://zy-cdn.rzhushou.com',
    'https://cdn.tingyou.fun',
    'https://cdn.yuyue27.com',
    'https://cdn.ukilive.com',
];

// app短链域名集合
export const JSBRIDGE_HOST = [
    'tt://m.52tt.com',
    'tyf://m.tses.net',
    'ymic://m.tingyou.fun',
    'tmj://m.ukilive.com',
];

// 导出当前环境对应的页面域名
export const webHost = `${urlConfig[`web_${osType}_host`][env]}`;
export const webHostShare = `${urlConfig[`web_${osType}_share_host`][env]}`;

// 是否PC客户端
export const isPCTT = myWebview.params.app === 'pc' || myWebview.params.os_type === 'pc';
