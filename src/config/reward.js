export const rewardList = [
    {
        "resource_id": "BP_Gift1",
        "testingName": "2月底活动-10元礼物",
        "name": "2月底活动-10元礼物",
        "type": "package",
        "mark": "包裹",
        "module": "等级奖励",
        "unit": "个",
        "price": 1000,
        "special_type": "豆豆礼物",
        "image": "BP_Gift1.png"
    },
    {
        "resource_id": "BP_Gift2",
        "testingName": "女神来了",
        "name": "女神来了",
        "type": "package",
        "mark": "包裹",
        "module": "等级奖励",
        "unit": "个",
        "price": 5000,
        "special_type": "豆豆礼物",
        "image": "BP_Gift2.png"
    },
    {
        "resource_id": "BP_Gift3",
        "testingName": "女生节红钻100",
        "name": "女生节红钻100",
        "type": "package",
        "mark": "包裹",
        "module": "等级奖励",
        "unit": "个",
        "price": 100,
        "special_type": "红钻礼物",
        "image": "BP_Gift3.png",
        "image_url": ""
    },
    {
        "resource_id": "BP_Gift4",
        "testingName": "大冒险",
        "name": "大冒险",
        "type": "package",
        "mark": "包裹",
        "module": "等级奖励",
        "unit": "个",
        "price": 1000,
        "special_type": "红钻礼物",
        "image": "BP_Gift4.png"
    },
    {
        "resource_id": "BP_CS1",
        "testingName": "0.1元lv2冰沙",
        "name": "0.1元lv2冰沙",
        "type": "headwear",
        "mark": "麦位框",
        "module": "等级奖励",
        "unit": "天",
        "special_type": "",
        "image": "BP_CS1.png"
    },
    {
        "resource_id": "BP_CS2",
        "testingName": "豹豹lv3",
        "name": "豹豹lv3",
        "type": "mount",
        "mark": "坐骑",
        "module": "等级奖励",
        "unit": "天",
        "special_type": "",
        "image": "BP_CS2.png"
    },
    {
        "resource_id": "BP_CS3",
        "testingName": "一曲定情",
        "name": "一曲定情",
        "type": "flutter",
        "mark": "主页飘",
        "module": "等级奖励",
        "unit": "天",
        "special_type": "",
        "image": "BP_CS3.png"
    },
    {
        "resource_id": "BP_CS4",
        "testingName": "完美人格",
        "name": "完美人格",
        "type": "user_plate",
        "mark": "个人铭牌",
        "module": "等级奖励",
        "unit": "天",
        "special_type": "",
        "image": "BP_CS4.png"
    },
    {
        "resource_id": "BP_CS5",
        "testingName": "欢庆年度",
        "name": "欢庆年度",
        "type": "ugc_bg",
        "mark": "个人房间背景",
        "module": "等级奖励",
        "unit": "天",
        "special_type": "静态背景",
        "image": "BP_CS5.png"
    },
    {
        "resource_id": "BP_Coin",
        "testingName": "贝壳",
        "name": "贝壳",
        "type": "other",
        "mark": "自定义名称的礼物类型",
        "module": "等级奖励",
        "unit": "天",
        "special_type": "自定义",
        "image": "BP_Coin.png"
    },
    {
        "resource_id": "BP_GUR1",
        "testingName": "25年愚人节10元",
        "name": "25年愚人节10元",
        "type": "present_privilege",
        "mark": "礼物赠送权",
        "module": "等级奖励",
        "unit": "天",
        "price": 5000,
        "special_type": "礼物架豆豆礼物",
        "image": "BP_GUR1.png"
    },
    {
        "resource_id": "BP_GUR2",
        "testingName": "林深时见羊",
        "name": "林深时见羊",
        "type": "present_privilege",
        "mark": "礼物赠送权",
        "module": "等级奖励",
        "unit": "天",
        "price": 10000,
        "special_type": "礼物架豆豆礼物",
        "image": "BP_GUR2.png"
    },
    {
        "resource_id": "BOX_Gift1",
        "testingName": "草莓酱",
        "name": "草莓酱",
        "type": "package",
        "mark": "包裹",
        "module": "宝箱奖励",
        "unit": "个",
        "price": 100,
        "special_type": "豆豆礼物",
        "image": "BOX_Gift1.png"
    },
    {
        "resource_id": "BOX_Gift2",
        "testingName": "礼物压缩测试v3",
        "name": "礼物压缩测试v3",
        "type": "package",
        "mark": "包裹",
        "module": "宝箱奖励",
        "unit": "个",
        "price": 1000,
        "special_type": "豆豆礼物",
        "image": "BOX_Gift2.png"
    },
    {
        "resource_id": "BOX_Gift3",
        "testingName": "画扇",
        "name": "画扇",
        "type": "package",
        "mark": "包裹",
        "module": "宝箱奖励",
        "unit": "个",
        "price": 5000,
        "special_type": "豆豆礼物",
        "image": "BOX_Gift3.png"
    },
    {
        "resource_id": "BOX_Gift4",
        "testingName": "营收100男女",
        "name": "营收100男女",
        "type": "package",
        "mark": "包裹",
        "module": "宝箱奖励",
        "unit": "个",
        "price": 10000,
        "special_type": "豆豆礼物",
        "image": "BOX_Gift4.png"
    },
    {
        "resource_id": "BOX_Gift5",
        "testingName": "感恩节520",
        "name": "感恩节520",
        "type": "package",
        "mark": "包裹",
        "module": "宝箱奖励",
        "unit": "个",
        "price": 52000,
        "special_type": "豆豆礼物",
        "image": "BOX_Gift5.png"
    },
    {
        "resource_id": "BOX_Gift6",
        "testingName": "万物生长",
        "name": "万物生长",
        "type": "package",
        "mark": "包裹",
        "module": "宝箱奖励",
        "unit": "个",
        "price": 131400,
        "special_type": "豆豆礼物",
        "image": "BOX_Gift6.png"
    },
    {
        "resource_id": "BOX_Gift7",
        "testingName": "女生节红钻100",
        "name": "女生节红钻100",
        "type": "package",
        "mark": "包裹",
        "module": "宝箱奖励",
        "unit": "个",
        "price": 100,
        "special_type": "红钻礼物",
        "image": "BOX_Gift7.png"
    },
    {
        "resource_id": "BOX_Gift8",
        "testingName": "大冒险",
        "name": "大冒险",
        "type": "package",
        "mark": "包裹",
        "module": "宝箱奖励",
        "unit": "个",
        "price": 1000,
        "special_type": "红钻礼物",
        "image": "BOX_Gift8.png"
    },
    {
        "resource_id": "BOX_CS1",
        "testingName": "0.1元lv2葡萄",
        "name": "0.1元lv2葡萄",
        "type": "headwear",
        "mark": "麦位框",
        "module": "宝箱奖励",
        "unit": "天",
        "special_type": "",
        "image": "BOX_CS1.png"
    },
    {
        "resource_id": "BOX_CS2",
        "testingName": "女剑士",
        "name": "女剑士",
        "type": "mount",
        "mark": "坐骑",
        "module": "宝箱奖励",
        "unit": "天",
        "special_type": "",
        "image": "BOX_CS2.png"
    },
    {
        "resource_id": "BOX_CS3",
        "testingName": "玩偶A",
        "name": "玩偶A",
        "type": "flutter",
        "mark": "主页飘",
        "module": "宝箱奖励",
        "unit": "天",
        "special_type": "",
        "image": "BOX_CS3.png"
    },
    {
        "resource_id": "EX_GUR1",
        "testingName": "25年愚人节10元",
        "name": "25年愚人节10元",
        "type": "present_privilege",
        "mark": "礼物赠送权",
        "module": "兑换奖励",
        "unit": "天",
        "price": 5000,
        "special_type": "礼物架豆豆礼物",
        "image": "EX_GUR1.png"
    },
    {
        "resource_id": "EX_GUR2",
        "testingName": "林深时见羊",
        "name": "林深时见羊",
        "type": "present_privilege",
        "mark": "礼物赠送权",
        "module": "兑换奖励",
        "unit": "天",
        "price": 10000,
        "special_type": "礼物架豆豆礼物",
        "image": "EX_GUR2.png"
    },
    {
        "resource_id": "EX_Card1",
        "testingName": "侯爵体验卡",
        "name": "侯爵体验卡",
        "type": "other",
        "mark": "体验卡",
        "module": "兑换奖励",
        "unit": "天",
        "special_type": "自定义",
        "image": "EX_Card1.png"
    },
    {
        "resource_id": "EX_Card2",
        "testingName": "公爵体验卡",
        "name": "公爵体验卡",
        "type": "other",
        "mark": "体验卡",
        "module": "兑换奖励",
        "unit": "天",
        "special_type": "自定义",
        "image": "EX_Card2.png"
    },
    {
        "resource_id": "EX_CS1",
        "testingName": "0.1元lv2咖啡",
        "name": "0.1元lv2咖啡",
        "type": "headwear",
        "mark": "麦位框",
        "module": "兑换奖励",
        "unit": "天",
        "special_type": "",
        "image": "EX_CS1.png"
    },
    {
        "resource_id": "EX_CS2",
        "testingName": "周年庆f复用",
        "name": "周年庆f复用",
        "type": "mount",
        "mark": "坐骑",
        "module": "兑换奖励",
        "unit": "天",
        "special_type": "",
        "image": "EX_CS2.png"
    },
    {
        "resource_id": "EX_CS3",
        "testingName": "旋转木马",
        "name": "旋转木马",
        "type": "flutter",
        "mark": "主页飘",
        "module": "兑换奖励",
        "unit": "天",
        "special_type": "",
        "image": "EX_CS3.png"
    },
    {
        "resource_id": "EX_CS4",
        "testingName": "天下萌主",
        "name": "天下萌主",
        "type": "user_plate",
        "mark": "个人铭牌",
        "module": "兑换奖励",
        "unit": "天",
        "special_type": "",
        "image": "EX_CS4.png"
    },
    {
        "resource_id": "EX_CS5",
        "testingName": "欢庆年度",
        "name": "欢庆年度",
        "type": "ugc_bg",
        "mark": "个人房间背景",
        "module": "兑换奖励",
        "unit": "天",
        "special_type": "静态背景",
        "image": "EX_CS5.png"
    },
    {
        "resource_id": "TOP_Gift1",
        "testingName": "游轮婚礼",
        "name": "游轮婚礼",
        "type": "package",
        "mark": "包裹",
        "module": "榜单奖励",
        "unit": "个",
        "price": 131400,
        "special_type": "豆豆礼物",
        "image": "TOP_Gift1.png"
    },
    {
        "resource_id": "TOP_Gift2",
        "testingName": "小城堡",
        "name": "小城堡",
        "type": "package",
        "mark": "包裹",
        "module": "榜单奖励",
        "unit": "个",
        "price": 88888,
        "special_type": "豆豆礼物",
        "image": "TOP_Gift2.png"
    },
    {
        "resource_id": "TOP_Gift3",
        "testingName": "52000豆上流舞会-2659",
        "name": "52000豆上流舞会-2659",
        "type": "package",
        "mark": "包裹",
        "module": "榜单奖励",
        "unit": "个",
        "price": 52000,
        "special_type": "豆豆礼物",
        "image": "TOP_Gift3.png"
    },
    {
        "resource_id": "TOP_Gift4",
        "testingName": "新年舞狮",
        "name": "新年舞狮",
        "type": "package",
        "mark": "包裹",
        "module": "榜单奖励",
        "unit": "个",
        "price": 20000,
        "special_type": "豆豆礼物",
        "image": "TOP_Gift4.png"
    },
    {
        "resource_id": "TOP_Gift5",
        "testingName": "甜品摊位100",
        "name": "甜品摊位100",
        "type": "package",
        "mark": "包裹",
        "module": "榜单奖励",
        "unit": "个",
        "price": 10000,
        "special_type": "豆豆礼物",
        "image": "TOP_Gift5.png"
    },
    {
        "resource_id": "TOP_CS1",
        "testingName": "0.1元lv2草莓",
        "name": "0.1元lv2草莓",
        "type": "headwear",
        "mark": "麦位框",
        "module": "榜单奖励",
        "unit": "天",
        "special_type": "",
        "image": "TOP_CS1.png"
    },
    {
        "resource_id": "TOP_CS2",
        "testingName": "3月会员抽奖小猫咪",
        "name": "3月会员抽奖小猫咪",
        "type": "mount",
        "mark": "坐骑",
        "module": "榜单奖励",
        "unit": "天",
        "special_type": "",
        "image": "TOP_CS2.png"
    },
    {
        "resource_id": "LE_GUR1",
        "testingName": "林深时见羊",
        "name": "林深时见羊",
        "type": "present_privilege",
        "mark": "礼物赠送权",
        "module": "限定任务",
        "unit": "天",
        "price": 10000,
        "special_type": "礼物架豆豆礼物",
        "image": "LE_GUR1.png"
    }
]

export const rewardMap = {
    "BP_Gift1": {
        "resource_id": "BP_Gift1",
        "testingName": "2月底活动-10元礼物",
        "name": "2月底活动-10元礼物",
        "type": "package",
        "mark": "包裹",
        "module": "等级奖励",
        "unit": "个",
        "price": 1000,
        "special_type": "豆豆礼物",
        "image": "BP_Gift1.png"
    },
    "BP_Gift2": {
        "resource_id": "BP_Gift2",
        "testingName": "女神来了",
        "name": "女神来了",
        "type": "package",
        "mark": "包裹",
        "module": "等级奖励",
        "unit": "个",
        "price": 5000,
        "special_type": "豆豆礼物",
        "image": "BP_Gift2.png"
    },
    "BP_Gift3": {
        "resource_id": "BP_Gift3",
        "testingName": "女生节红钻100",
        "name": "女生节红钻100",
        "type": "package",
        "mark": "包裹",
        "module": "等级奖励",
        "unit": "个",
        "price": 100,
        "special_type": "红钻礼物",
        "image": "BP_Gift3.png",
        "image_url": ""
    },
    "BP_Gift4": {
        "resource_id": "BP_Gift4",
        "testingName": "大冒险",
        "name": "大冒险",
        "type": "package",
        "mark": "包裹",
        "module": "等级奖励",
        "unit": "个",
        "price": 1000,
        "special_type": "红钻礼物",
        "image": "BP_Gift4.png"
    },
    "BP_CS1": {
        "resource_id": "BP_CS1",
        "testingName": "0.1元lv2冰沙",
        "name": "0.1元lv2冰沙",
        "type": "headwear",
        "mark": "麦位框",
        "module": "等级奖励",
        "unit": "天",
        "special_type": "",
        "image": "BP_CS1.png"
    },
    "BP_CS2": {
        "resource_id": "BP_CS2",
        "testingName": "豹豹lv3",
        "name": "豹豹lv3",
        "type": "mount",
        "mark": "坐骑",
        "module": "等级奖励",
        "unit": "天",
        "special_type": "",
        "image": "BP_CS2.png"
    },
    "BP_CS3": {
        "resource_id": "BP_CS3",
        "testingName": "一曲定情",
        "name": "一曲定情",
        "type": "flutter",
        "mark": "主页飘",
        "module": "等级奖励",
        "unit": "天",
        "special_type": "",
        "image": "BP_CS3.png"
    },
    "BP_CS4": {
        "resource_id": "BP_CS4",
        "testingName": "完美人格",
        "name": "完美人格",
        "type": "user_plate",
        "mark": "个人铭牌",
        "module": "等级奖励",
        "unit": "天",
        "special_type": "",
        "image": "BP_CS4.png"
    },
    "BP_CS5": {
        "resource_id": "BP_CS5",
        "testingName": "欢庆年度",
        "name": "欢庆年度",
        "type": "ugc_bg",
        "mark": "个人房间背景",
        "module": "等级奖励",
        "unit": "天",
        "special_type": "静态背景",
        "image": "BP_CS5.png"
    },
    "BP_Coin": {
        "resource_id": "BP_Coin",
        "testingName": "贝壳",
        "name": "贝壳",
        "type": "other",
        "mark": "自定义名称的礼物类型",
        "module": "等级奖励",
        "unit": "天",
        "special_type": "自定义",
        "image": "BP_Coin.png"
    },
    "BP_GUR1": {
        "resource_id": "BP_GUR1",
        "testingName": "25年愚人节10元",
        "name": "25年愚人节10元",
        "type": "present_privilege",
        "mark": "礼物赠送权",
        "module": "等级奖励",
        "unit": "天",
        "price": 5000,
        "special_type": "礼物架豆豆礼物",
        "image": "BP_GUR1.png"
    },
    "BP_GUR2": {
        "resource_id": "BP_GUR2",
        "testingName": "林深时见羊",
        "name": "林深时见羊",
        "type": "present_privilege",
        "mark": "礼物赠送权",
        "module": "等级奖励",
        "unit": "天",
        "price": 10000,
        "special_type": "礼物架豆豆礼物",
        "image": "BP_GUR2.png"
    },
    "BOX_Gift1": {
        "resource_id": "BOX_Gift1",
        "testingName": "草莓酱",
        "name": "草莓酱",
        "type": "package",
        "mark": "包裹",
        "module": "宝箱奖励",
        "unit": "个",
        "price": 100,
        "special_type": "豆豆礼物",
        "image": "BOX_Gift1.png"
    },
    "BOX_Gift2": {
        "resource_id": "BOX_Gift2",
        "testingName": "礼物压缩测试v3",
        "name": "礼物压缩测试v3",
        "type": "package",
        "mark": "包裹",
        "module": "宝箱奖励",
        "unit": "个",
        "price": 1000,
        "special_type": "豆豆礼物",
        "image": "BOX_Gift2.png"
    },
    "BOX_Gift3": {
        "resource_id": "BOX_Gift3",
        "testingName": "画扇",
        "name": "画扇",
        "type": "package",
        "mark": "包裹",
        "module": "宝箱奖励",
        "unit": "个",
        "price": 5000,
        "special_type": "豆豆礼物",
        "image": "BOX_Gift3.png"
    },
    "BOX_Gift4": {
        "resource_id": "BOX_Gift4",
        "testingName": "营收100男女",
        "name": "营收100男女",
        "type": "package",
        "mark": "包裹",
        "module": "宝箱奖励",
        "unit": "个",
        "price": 10000,
        "special_type": "豆豆礼物",
        "image": "BOX_Gift4.png"
    },
    "BOX_Gift5": {
        "resource_id": "BOX_Gift5",
        "testingName": "感恩节520",
        "name": "感恩节520",
        "type": "package",
        "mark": "包裹",
        "module": "宝箱奖励",
        "unit": "个",
        "price": 52000,
        "special_type": "豆豆礼物",
        "image": "BOX_Gift5.png"
    },
    "BOX_Gift6": {
        "resource_id": "BOX_Gift6",
        "testingName": "万物生长",
        "name": "万物生长",
        "type": "package",
        "mark": "包裹",
        "module": "宝箱奖励",
        "unit": "个",
        "price": 131400,
        "special_type": "豆豆礼物",
        "image": "BOX_Gift6.png"
    },
    "BOX_Gift7": {
        "resource_id": "BOX_Gift7",
        "testingName": "女生节红钻100",
        "name": "女生节红钻100",
        "type": "package",
        "mark": "包裹",
        "module": "宝箱奖励",
        "unit": "个",
        "price": 100,
        "special_type": "红钻礼物",
        "image": "BOX_Gift7.png"
    },
    "BOX_Gift8": {
        "resource_id": "BOX_Gift8",
        "testingName": "大冒险",
        "name": "大冒险",
        "type": "package",
        "mark": "包裹",
        "module": "宝箱奖励",
        "unit": "个",
        "price": 1000,
        "special_type": "红钻礼物",
        "image": "BOX_Gift8.png"
    },
    "BOX_CS1": {
        "resource_id": "BOX_CS1",
        "testingName": "0.1元lv2葡萄",
        "name": "0.1元lv2葡萄",
        "type": "headwear",
        "mark": "麦位框",
        "module": "宝箱奖励",
        "unit": "天",
        "special_type": "",
        "image": "BOX_CS1.png"
    },
    "BOX_CS2": {
        "resource_id": "BOX_CS2",
        "testingName": "女剑士",
        "name": "女剑士",
        "type": "mount",
        "mark": "坐骑",
        "module": "宝箱奖励",
        "unit": "天",
        "special_type": "",
        "image": "BOX_CS2.png"
    },
    "BOX_CS3": {
        "resource_id": "BOX_CS3",
        "testingName": "玩偶A",
        "name": "玩偶A",
        "type": "flutter",
        "mark": "主页飘",
        "module": "宝箱奖励",
        "unit": "天",
        "special_type": "",
        "image": "BOX_CS3.png"
    },
    "EX_GUR1": {
        "resource_id": "EX_GUR1",
        "testingName": "25年愚人节10元",
        "name": "25年愚人节10元",
        "type": "present_privilege",
        "mark": "礼物赠送权",
        "module": "兑换奖励",
        "unit": "天",
        "price": 5000,
        "special_type": "礼物架豆豆礼物",
        "image": "EX_GUR1.png"
    },
    "EX_GUR2": {
        "resource_id": "EX_GUR2",
        "testingName": "林深时见羊",
        "name": "林深时见羊",
        "type": "present_privilege",
        "mark": "礼物赠送权",
        "module": "兑换奖励",
        "unit": "天",
        "price": 10000,
        "special_type": "礼物架豆豆礼物",
        "image": "EX_GUR2.png"
    },
    "EX_Card1": {
        "resource_id": "EX_Card1",
        "testingName": "侯爵体验卡",
        "name": "侯爵体验卡",
        "type": "other",
        "mark": "体验卡",
        "module": "兑换奖励",
        "unit": "天",
        "special_type": "自定义",
        "image": "EX_Card1.png"
    },
    "EX_Card2": {
        "resource_id": "EX_Card2",
        "testingName": "公爵体验卡",
        "name": "公爵体验卡",
        "type": "other",
        "mark": "体验卡",
        "module": "兑换奖励",
        "unit": "天",
        "special_type": "自定义",
        "image": "EX_Card2.png"
    },
    "EX_CS1": {
        "resource_id": "EX_CS1",
        "testingName": "0.1元lv2咖啡",
        "name": "0.1元lv2咖啡",
        "type": "headwear",
        "mark": "麦位框",
        "module": "兑换奖励",
        "unit": "天",
        "special_type": "",
        "image": "EX_CS1.png"
    },
    "EX_CS2": {
        "resource_id": "EX_CS2",
        "testingName": "周年庆f复用",
        "name": "周年庆f复用",
        "type": "mount",
        "mark": "坐骑",
        "module": "兑换奖励",
        "unit": "天",
        "special_type": "",
        "image": "EX_CS2.png"
    },
    "EX_CS3": {
        "resource_id": "EX_CS3",
        "testingName": "旋转木马",
        "name": "旋转木马",
        "type": "flutter",
        "mark": "主页飘",
        "module": "兑换奖励",
        "unit": "天",
        "special_type": "",
        "image": "EX_CS3.png"
    },
    "EX_CS4": {
        "resource_id": "EX_CS4",
        "testingName": "天下萌主",
        "name": "天下萌主",
        "type": "user_plate",
        "mark": "个人铭牌",
        "module": "兑换奖励",
        "unit": "天",
        "special_type": "",
        "image": "EX_CS4.png"
    },
    "EX_CS5": {
        "resource_id": "EX_CS5",
        "testingName": "欢庆年度",
        "name": "欢庆年度",
        "type": "ugc_bg",
        "mark": "个人房间背景",
        "module": "兑换奖励",
        "unit": "天",
        "special_type": "静态背景",
        "image": "EX_CS5.png"
    },
    "TOP_Gift1": {
        "resource_id": "TOP_Gift1",
        "testingName": "游轮婚礼",
        "name": "游轮婚礼",
        "type": "package",
        "mark": "包裹",
        "module": "榜单奖励",
        "unit": "个",
        "price": 131400,
        "special_type": "豆豆礼物",
        "image": "TOP_Gift1.png"
    },
    "TOP_Gift2": {
        "resource_id": "TOP_Gift2",
        "testingName": "小城堡",
        "name": "小城堡",
        "type": "package",
        "mark": "包裹",
        "module": "榜单奖励",
        "unit": "个",
        "price": 88888,
        "special_type": "豆豆礼物",
        "image": "TOP_Gift2.png"
    },
    "TOP_Gift3": {
        "resource_id": "TOP_Gift3",
        "testingName": "52000豆上流舞会-2659",
        "name": "52000豆上流舞会-2659",
        "type": "package",
        "mark": "包裹",
        "module": "榜单奖励",
        "unit": "个",
        "price": 52000,
        "special_type": "豆豆礼物",
        "image": "TOP_Gift3.png"
    },
    "TOP_Gift4": {
        "resource_id": "TOP_Gift4",
        "testingName": "新年舞狮",
        "name": "新年舞狮",
        "type": "package",
        "mark": "包裹",
        "module": "榜单奖励",
        "unit": "个",
        "price": 20000,
        "special_type": "豆豆礼物",
        "image": "TOP_Gift4.png"
    },
    "TOP_Gift5": {
        "resource_id": "TOP_Gift5",
        "testingName": "甜品摊位100",
        "name": "甜品摊位100",
        "type": "package",
        "mark": "包裹",
        "module": "榜单奖励",
        "unit": "个",
        "price": 10000,
        "special_type": "豆豆礼物",
        "image": "TOP_Gift5.png"
    },
    "TOP_CS1": {
        "resource_id": "TOP_CS1",
        "testingName": "0.1元lv2草莓",
        "name": "0.1元lv2草莓",
        "type": "headwear",
        "mark": "麦位框",
        "module": "榜单奖励",
        "unit": "天",
        "special_type": "",
        "image": "TOP_CS1.png"
    },
    "TOP_CS2": {
        "resource_id": "TOP_CS2",
        "testingName": "3月会员抽奖小猫咪",
        "name": "3月会员抽奖小猫咪",
        "type": "mount",
        "mark": "坐骑",
        "module": "榜单奖励",
        "unit": "天",
        "special_type": "",
        "image": "TOP_CS2.png"
    },
    "LE_GUR1": {
        "resource_id": "LE_GUR1",
        "testingName": "林深时见羊",
        "name": "林深时见羊",
        "type": "present_privilege",
        "mark": "礼物赠送权",
        "module": "限定任务",
        "unit": "天",
        "price": 10000,
        "special_type": "礼物架豆豆礼物",
        "image": "LE_GUR1.png"
    }
}

