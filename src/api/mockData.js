import mock from './mock';

const API = {
    init() {
        return {
            serverTime: 1753346964,
            startTime: 1753346964,
            endTime: 1753346964,
            hasBuyBattlePass: false,
            battlePassType: 1,
            exp: 2,
            passStartDate: 1,
            hasBuyLevel: 1,
            everyLevelPrize: 2,
            buyLevelLimit: 1,
            everyLimitExp: 2,
            openRisk: 2,
            advancedBattlePassPrice: 2,
            superBattlePassPrice: 1,
            hasOpenBox: 1,
            nextCanOpenTime: 1753346964,
            openBoxExtraExp: 2,
            maxLevel: 2,
        };
    },
    upgradeBattlePass() {
        return {
            getExtraExp: 2,
            rewards: [
                { id: 'Mock-id', num: 2, time: 1753346964 },
                { id: 'Mock-id', num: 2, time: 1753346964 },
                { id: 'Mock-id', num: 2, time: 1753346964 },
                { id: 'Mock-id', num: 2, time: 1753346964 },
                { id: 'Mock-id', num: 2, time: 1753346964 },
                { id: 'Mock-id', num: 2, time: 1753346964 },
                { id: 'Mock-id', num: 2, time: 1753346964 },
                { id: 'Mock-id', num: 2, time: 1753346964 },
                { id: 'Mock-id', num: 2, time: 1753346964 },
                { id: 'Mock-id', num: 2, time: 1753346964 },
            ],
            success: false,
        };
    },
    receiveReward() {
        return {
            rewards: [
                { id: 'Mock-id', num: 1, time: 1753346964 },
                { id: 'Mock-id', num: 1, time: 1753346964 },
                { id: 'Mock-id', num: 1, time: 1753346964 },
                { id: 'Mock-id', num: 1, time: 1753346964 },
                { id: 'Mock-id', num: 1, time: 1753346964 },
                { id: 'Mock-id', num: 1, time: 1753346964 },
                { id: 'Mock-id', num: 1, time: 1753346964 },
                { id: 'Mock-id', num: 1, time: 1753346964 },
                { id: 'Mock-id', num: 1, time: 1753346964 },
                { id: 'Mock-id', num: 1, time: 1753346964 },
            ],
        };
    },
    receiveAll() {
        return {
            rewards: [
                { id: 'Mock-id', num: 1, time: 1753346964 },
                { id: 'Mock-id', num: 1, time: 1753346964 },
                { id: 'Mock-id', num: 1, time: 1753346964 },
                { id: 'Mock-id', num: 1, time: 1753346964 },
                { id: 'Mock-id', num: 1, time: 1753346964 },
                { id: 'Mock-id', num: 1, time: 1753346964 },
                { id: 'Mock-id', num: 1, time: 1753346964 },
                { id: 'Mock-id', num: 1, time: 1753346964 },
                { id: 'Mock-id', num: 1, time: 1753346964 },
                { id: 'Mock-id', num: 1, time: 1753346964 },
            ],
        };
    },
    battlePassTaskProcess() {
        return {
            list: [
                {
                    process: 1,
                    total: 1,
                    finishTimes: 1753346964,
                    name: 'Mock-name',
                    exp: 1,
                    stages: [
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                    ],
                    startTime: 1753346964,
                    endTime: 1753346964,
                },
                {
                    process: 1,
                    total: 1,
                    finishTimes: 1753346964,
                    name: 'Mock-name',
                    exp: 1,
                    stages: [
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                    ],
                    startTime: 1753346964,
                    endTime: 1753346964,
                },
                {
                    process: 1,
                    total: 1,
                    finishTimes: 1753346964,
                    name: 'Mock-name',
                    exp: 1,
                    stages: [
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                    ],
                    startTime: 1753346964,
                    endTime: 1753346964,
                },
                {
                    process: 1,
                    total: 1,
                    finishTimes: 1753346964,
                    name: 'Mock-name',
                    exp: 1,
                    stages: [
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                    ],
                    startTime: 1753346964,
                    endTime: 1753346964,
                },
                {
                    process: 1,
                    total: 1,
                    finishTimes: 1753346964,
                    name: 'Mock-name',
                    exp: 1,
                    stages: [
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                    ],
                    startTime: 1753346964,
                    endTime: 1753346964,
                },
                {
                    process: 1,
                    total: 1,
                    finishTimes: 1753346964,
                    name: 'Mock-name',
                    exp: 1,
                    stages: [
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                    ],
                    startTime: 1753346964,
                    endTime: 1753346964,
                },
                {
                    process: 1,
                    total: 1,
                    finishTimes: 1753346964,
                    name: 'Mock-name',
                    exp: 1,
                    stages: [
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                    ],
                    startTime: 1753346964,
                    endTime: 1753346964,
                },
                {
                    process: 1,
                    total: 1,
                    finishTimes: 1753346964,
                    name: 'Mock-name',
                    exp: 1,
                    stages: [
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                    ],
                    startTime: 1753346964,
                    endTime: 1753346964,
                },
                {
                    process: 1,
                    total: 1,
                    finishTimes: 1753346964,
                    name: 'Mock-name',
                    exp: 1,
                    stages: [
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                    ],
                    startTime: 1753346964,
                    endTime: 1753346964,
                },
                {
                    process: 1,
                    total: 1,
                    finishTimes: 1753346964,
                    name: 'Mock-name',
                    exp: 1,
                    stages: [
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                        { value: 2, exp: 1, status: 1 },
                    ],
                    startTime: 1753346964,
                    endTime: 1753346964,
                },
            ],
        };
    },
    battlePassReceiveRecord() {
        return {
            records: [
                { level: 2, type: 1 },
                { level: 2, type: 1 },
                { level: 2, type: 1 },
                { level: 2, type: 1 },
                { level: 2, type: 1 },
                { level: 2, type: 1 },
                { level: 2, type: 1 },
                { level: 2, type: 1 },
                { level: 2, type: 1 },
                { level: 2, type: 1 },
            ],
            allRewards: {
                normalCard: {
                    tmpListData: [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}],
                    rewards: {
                        tmpListData: [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}],
                        id: 'Mock-id',
                        num: 1,
                        time: 1753346964,
                    },
                },
                advancedCard: {
                    tmpListData: [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}],
                    rewards: {
                        tmpListData: [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}],
                        id: 'Mock-id',
                        num: 1,
                        time: 1753346964,
                    },
                },
                superCard: {
                    tmpListData: [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}],
                    rewards: {
                        tmpListData: [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}],
                        id: 'Mock-id',
                        num: 1,
                        time: 1753346964,
                    },
                },
            },
        };
    },
    lotteryData() {
        return { shell: 1 };
    },
    lottery() {
        return {
            rewards: [
                { id: 'Mock-id', num: 1, time: 1753346964 },
                { id: 'Mock-id', num: 1, time: 1753346964 },
                { id: 'Mock-id', num: 1, time: 1753346964 },
                { id: 'Mock-id', num: 1, time: 1753346964 },
                { id: 'Mock-id', num: 1, time: 1753346964 },
                { id: 'Mock-id', num: 1, time: 1753346964 },
                { id: 'Mock-id', num: 1, time: 1753346964 },
                { id: 'Mock-id', num: 1, time: 1753346964 },
                { id: 'Mock-id', num: 1, time: 1753346964 },
                { id: 'Mock-id', num: 1, time: 1753346964 },
            ],
        };
    },
    lotteryRecord() {
        return {
            records: [
                {
                    reward: [
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                    ],
                    extraParam: {},
                    time: 1753346964,
                },
                {
                    reward: [
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                    ],
                    extraParam: {},
                    time: 1753346964,
                },
                {
                    reward: [
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                    ],
                    extraParam: {},
                    time: 1753346964,
                },
                {
                    reward: [
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                    ],
                    extraParam: {},
                    time: 1753346964,
                },
                {
                    reward: [
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                    ],
                    extraParam: {},
                    time: 1753346964,
                },
                {
                    reward: [
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                    ],
                    extraParam: {},
                    time: 1753346964,
                },
                {
                    reward: [
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                    ],
                    extraParam: {},
                    time: 1753346964,
                },
                {
                    reward: [
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                    ],
                    extraParam: {},
                    time: 1753346964,
                },
                {
                    reward: [
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                    ],
                    extraParam: {},
                    time: 1753346964,
                },
                {
                    reward: [
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                        { id: 'Mock-id', num: 2, time: 1753346964 },
                    ],
                    extraParam: {},
                    time: 1753346964,
                },
            ],
            total: 2,
        };
    },
    getUserTbean() {
        return { tbean: 1 };
    },
    buyRiskControl() {
        return { errCode: 1, errMsg: 'Mock-errMsg', errInfo: 'Mock-errInfo' };
    },
    openTreasureBox() {
        return { exp: 2, extraExp: 2 };
    },
    giftRank() {
        return {
            total: 1,
            list: [
                {
                    uid: 2416206,
                    rank: 2,
                    rankHuman: 'Mock-rankHuman',
                    value: 2,
                    valueHuman: 'Mock-valueHuman',
                    ltPrevValue: 1,
                    ltPrevValueHuman: 'Mock-ltPrevValueHuman',
                    gtNextValue: 2,
                    gtNextValueHuman: 'Mock-gtNextValueHuman',
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 2,
                        guildInfo: { name: 'Mock-name', guildId: 1, displayId: 1 },
                        role: 1,
                    },
                    channelInfo: { channelId: 2048111, status: 4 },
                    mvpInfoList: [
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                    ],
                },
                {
                    uid: 2416206,
                    rank: 2,
                    rankHuman: 'Mock-rankHuman',
                    value: 2,
                    valueHuman: 'Mock-valueHuman',
                    ltPrevValue: 1,
                    ltPrevValueHuman: 'Mock-ltPrevValueHuman',
                    gtNextValue: 2,
                    gtNextValueHuman: 'Mock-gtNextValueHuman',
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 2,
                        guildInfo: { name: 'Mock-name', guildId: 1, displayId: 1 },
                        role: 1,
                    },
                    channelInfo: { channelId: 2048111, status: 4 },
                    mvpInfoList: [
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                    ],
                },
                {
                    uid: 2416206,
                    rank: 2,
                    rankHuman: 'Mock-rankHuman',
                    value: 2,
                    valueHuman: 'Mock-valueHuman',
                    ltPrevValue: 1,
                    ltPrevValueHuman: 'Mock-ltPrevValueHuman',
                    gtNextValue: 2,
                    gtNextValueHuman: 'Mock-gtNextValueHuman',
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 2,
                        guildInfo: { name: 'Mock-name', guildId: 1, displayId: 1 },
                        role: 1,
                    },
                    channelInfo: { channelId: 2048111, status: 4 },
                    mvpInfoList: [
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                    ],
                },
                {
                    uid: 2416206,
                    rank: 2,
                    rankHuman: 'Mock-rankHuman',
                    value: 2,
                    valueHuman: 'Mock-valueHuman',
                    ltPrevValue: 1,
                    ltPrevValueHuman: 'Mock-ltPrevValueHuman',
                    gtNextValue: 2,
                    gtNextValueHuman: 'Mock-gtNextValueHuman',
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 2,
                        guildInfo: { name: 'Mock-name', guildId: 1, displayId: 1 },
                        role: 1,
                    },
                    channelInfo: { channelId: 2048111, status: 4 },
                    mvpInfoList: [
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                    ],
                },
                {
                    uid: 2416206,
                    rank: 2,
                    rankHuman: 'Mock-rankHuman',
                    value: 2,
                    valueHuman: 'Mock-valueHuman',
                    ltPrevValue: 1,
                    ltPrevValueHuman: 'Mock-ltPrevValueHuman',
                    gtNextValue: 2,
                    gtNextValueHuman: 'Mock-gtNextValueHuman',
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 2,
                        guildInfo: { name: 'Mock-name', guildId: 1, displayId: 1 },
                        role: 1,
                    },
                    channelInfo: { channelId: 2048111, status: 4 },
                    mvpInfoList: [
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                    ],
                },
                {
                    uid: 2416206,
                    rank: 2,
                    rankHuman: 'Mock-rankHuman',
                    value: 2,
                    valueHuman: 'Mock-valueHuman',
                    ltPrevValue: 1,
                    ltPrevValueHuman: 'Mock-ltPrevValueHuman',
                    gtNextValue: 2,
                    gtNextValueHuman: 'Mock-gtNextValueHuman',
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 2,
                        guildInfo: { name: 'Mock-name', guildId: 1, displayId: 1 },
                        role: 1,
                    },
                    channelInfo: { channelId: 2048111, status: 4 },
                    mvpInfoList: [
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                    ],
                },
                {
                    uid: 2416206,
                    rank: 2,
                    rankHuman: 'Mock-rankHuman',
                    value: 2,
                    valueHuman: 'Mock-valueHuman',
                    ltPrevValue: 1,
                    ltPrevValueHuman: 'Mock-ltPrevValueHuman',
                    gtNextValue: 2,
                    gtNextValueHuman: 'Mock-gtNextValueHuman',
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 2,
                        guildInfo: { name: 'Mock-name', guildId: 1, displayId: 1 },
                        role: 1,
                    },
                    channelInfo: { channelId: 2048111, status: 4 },
                    mvpInfoList: [
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                    ],
                },
                {
                    uid: 2416206,
                    rank: 2,
                    rankHuman: 'Mock-rankHuman',
                    value: 2,
                    valueHuman: 'Mock-valueHuman',
                    ltPrevValue: 1,
                    ltPrevValueHuman: 'Mock-ltPrevValueHuman',
                    gtNextValue: 2,
                    gtNextValueHuman: 'Mock-gtNextValueHuman',
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 2,
                        guildInfo: { name: 'Mock-name', guildId: 1, displayId: 1 },
                        role: 1,
                    },
                    channelInfo: { channelId: 2048111, status: 4 },
                    mvpInfoList: [
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                    ],
                },
                {
                    uid: 2416206,
                    rank: 2,
                    rankHuman: 'Mock-rankHuman',
                    value: 2,
                    valueHuman: 'Mock-valueHuman',
                    ltPrevValue: 1,
                    ltPrevValueHuman: 'Mock-ltPrevValueHuman',
                    gtNextValue: 2,
                    gtNextValueHuman: 'Mock-gtNextValueHuman',
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 2,
                        guildInfo: { name: 'Mock-name', guildId: 1, displayId: 1 },
                        role: 1,
                    },
                    channelInfo: { channelId: 2048111, status: 4 },
                    mvpInfoList: [
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                    ],
                },
                {
                    uid: 2416206,
                    rank: 2,
                    rankHuman: 'Mock-rankHuman',
                    value: 2,
                    valueHuman: 'Mock-valueHuman',
                    ltPrevValue: 1,
                    ltPrevValueHuman: 'Mock-ltPrevValueHuman',
                    gtNextValue: 2,
                    gtNextValueHuman: 'Mock-gtNextValueHuman',
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 2,
                        guildInfo: { name: 'Mock-name', guildId: 1, displayId: 1 },
                        role: 1,
                    },
                    channelInfo: { channelId: 2048111, status: 4 },
                    mvpInfoList: [
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                        {
                            rank: 2,
                            value: 2,
                            valueHuman: 'Mock-valueHuman',
                            userInfo: {
                                uid: 2416206,
                                username: 'tt110200509',
                                alias: 'Mock-alias',
                                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                                sex: 2,
                                guildInfo: { name: 'Mock-name', guildId: 1, displayId: 2 },
                                role: 2,
                            },
                        },
                    ],
                },
            ],
            self: {
                uid: 2416206,
                rank: 2,
                rankHuman: 'Mock-rankHuman',
                value: 1,
                valueHuman: 'Mock-valueHuman',
                ltPrevValue: 2,
                ltPrevValueHuman: 'Mock-ltPrevValueHuman',
                gtNextValue: 2,
                gtNextValueHuman: 'Mock-gtNextValueHuman',
                userInfo: {
                    uid: 2416206,
                    username: 'tt110200509',
                    alias: 'Mock-alias',
                    nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                    sex: 2,
                    guildInfo: { name: 'Mock-name', guildId: 2, displayId: 1 },
                    role: 1,
                },
                channelInfo: { channelId: 2048111, status: 3 },
                mvpInfoList: {
                    tmpListData: [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}],
                    rank: 2,
                    value: 2,
                    valueHuman: 'Mock-valueHuman',
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 2,
                        guildInfo: { name: 'Mock-name', guildId: 2, displayId: 1 },
                        role: 1,
                    },
                },
            },
        };
    },
    exchangeReward() {
        return { success: false, remainShellNum: 1 };
    },
    buyLevel() {
        return {};
    },
    getCids() {
        return { cids: [1] };
    },
    ...mock,
};

const getMockData = (type, payload) =>
    new Promise((resolve) => {
        const delay = Math.round(Math.random() * 10) * 50; // 模拟访问延迟
        let data;
        if (typeof API[type] === 'function')
            data = API[type](payload);
        else data = API[type];

        // eslint-disable-next-line no-console
        console.log(
            `模拟接口请求名称<=== ${type} delay: ${delay} ms; payload: ${JSON.stringify(payload)}`,
        );
        window.setTimeout(() => {
            // eslint-disable-next-line no-console
            console.log('模拟接口请求返回===>', data);
            resolve({
                code: 0,
                data,
                msg: '',
            });
        }, delay);
    });
export default getMockData;
