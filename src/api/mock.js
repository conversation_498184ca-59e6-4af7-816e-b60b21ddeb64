import dayjs from 'dayjs';
import { getDateTextTimeStamp } from '@/utils/time';

const currentLevel = 2;
const hasBuyLevel = 0;
const everyLimitExp = 1000;
const battlePassType = 2;

const TASKS = {
    1: [
        {
            process: 0,
            total: 1,
            name: '每日签到',
            exp: 50,
        },
        {
            name: '娱乐房间停留120S',
            process: 0,
            total: 3,
            exp: 100,
        },
        {
            name: '送任意豆豆礼物',
            process: 0,
            total: 1,
            exp: 100,
        },
        {
            name: '送出豆豆礼物金额到达',
            process: 100000100000,
            total: 100000,
            stages: [
                { value: 1000, exp: 50, status: 1 },
                { value: 5000, exp: 100, status: 1 },
                { value: 10000, exp: 100, status: 1 },
                { value: 30000, exp: 200, status: 1 },
                { value: 50000, exp: 200, status: 1 },
                { value: 100000, exp: 500, status: 1 },
            ],
        },
    ],
    2: [
        {
            name: '累计签到5天',
            process: 5,
            total: 5,
            exp: 1000,
            isDone: true,
        },
        {
            name: '累计消费300000豆',
            process: 0,
            total: 300000,
            exp: 1000,
        },
        {
            name: '打赏指定礼物10个',
            process: 10,
            total: 10,
            exp: 1000,
        },
    ],
    3: [
        {
            name: '累计签到30天',
            process: 0,
            total: 30,
            exp: 1000,
            isDone: false,
        },
        {
            name: '累计消费500000豆',
            process: 0,
            total: 500000,
            exp: 2000,
        },
        {
            name: '打赏指定礼物10个',
            process: 0,
            total: 10,
            exp: 2000,
        },
        {
            name: '完成豪华进阶',
            process: 0,
            total: 1,
            exp: 10000,
        },
    ],
    5: [
        {
            name: '打赏指定礼物10个',
            process: 0,
            total: 10,
            exp: 1000,
            startTime: dayjs('2025/07/20 00:00:00').unix(),
            endTime: dayjs('2025/07/27 00:00:00').unix(),
        },
    ],
};

export default {
    init() {
        return {
            serverTime: getDateTextTimeStamp('2025/08/25 00:00:00') - 5,
            startTime: getDateTextTimeStamp('2025/08/18 00:00:00'),
            endTime: getDateTextTimeStamp('2025/09/17 23:59:59'),
            battlePassType,
            exp: currentLevel * everyLimitExp + 156,
            hasBuyLevel,
            everyLevelPrize: 50000,
            buyLevelLimit: 20,
            everyLimitExp,
            openRisk: 0,
            advancedBattlePassPrice: 6800,
            superBattlePassPrice: 16800,
            hasOpenBox: true,
            nextCanOpenTime: getDateTextTimeStamp('2025/08/25 00:00:00'),
            openBoxExtraExp: 1000,
            maxLevel: 60,
        };
    },
    upgradeBattlePass() {
        return {
            getExtraExp: 1000,
            rewards: [
                { id: 'BP_Gift1', num: 1, time: 0 },
            ],
        };
    },
    battlePassTaskProcess({ type }) {
        return {
            list: TASKS[type] || [],
        };
    },
    receiveReward() {
        return {
            rewards: Array.from({ length: 7 }).fill(null).map(() => ({
                id: 'BP_Gift1',
                num: 1,
            })),
        };
    },
    receiveAll() {
        return {
            rewards: Array.from({ length: 10 }).fill(null).map(() => ({
                id: 'BP_Gift1',
                num: 1,
            })),
        };
    },
    battlePassReceiveRecord() {
        try {
            const normalCard = Array.from({ length: 60 + 1 })
                .fill(null)
                .map(() => ({
                    rewards: [{ id: 'BP_Coin', num: 1, time: 0 }],
                }));
            const advancedCard = Array.from({ length: 60 + 1 })
                .fill(null)
                .map(() => ({
                    rewards: [{ id: 'BP_Gift2', num: 1, time: 0 }],
                }));
            const superCard = Array.from({ length: 60 + 1 })
                .fill(null)
                .map(() => ({
                    rewards: [{ id: 'BP_Gift2', num: 1, time: 0 }],
                }));
            const records = [
                { level: 1, type: 1 },
            ];
            return {
                records,
                allRewards: {
                    normalCard,
                    advancedCard,
                    superCard,
                },
            };
        }
        catch (err) {
            console.log(err);
        }
    },
    getUserTbean() {
        return { tbean: 30000 };
    },
    buyRiskControl() {
        return { errCode: 1, errMsg: 'Mock-errMsg', errInfo: 'Mock-errInfo' };
    },
    openTreasureBox() {
        return { exp: 1000, extraExp: 0 };
    },
    buyLevel() {
        return {};
    },
    getCids() {
        return { cids: [1] };
    },
};
