// 执行pb.cjs命令后会覆盖掉手写的代码，请谨慎操作!
import request from '../utils/request';
import getMockData from './mockData';

export const fetchApi = ({ proPrefix = '/activity.Activity/', api, data = {}, config = {} }) => {
    const { mock } = myWebview.params;
    if (mock)
        return to(getMockData(api, data));

    const url = `${proPrefix}/${api}`.replace('//', '/');
    return to(request.post(url, data, config));
};

/**
 * ********************************
 * *********  活动接口 *************
 * ********************************
 */
const REQUEST_API_MAP = {
    init: 'init',
    upgradeBattlePass: 'upgradeBattlePass',
    receiveReward: 'receiveReward',
    receiveAll: 'receiveAll',
    battlePassTaskProcess: 'battlePassTaskProcess',
    battlePassReceiveRecord: 'battlePassReceiveRecord',
    lotteryData: 'lotteryData',
    lottery: 'lottery',
    lotteryRecord: 'lotteryRecord',
    getUserTbean: 'getUserTbean',
    buyRiskControl: 'buyRiskControl',
    openTreasureBox: 'openTreasureBox',
    giftRank: 'giftRank',
    exchangeReward: 'exchangeReward',
    buyLevel: 'buyLevel',
    getCids: 'getCids',
};

/** @type {function(import('./api.d.ts').InitReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').InitResp},any]>} */
export const init = (data, config) => fetchApi({ api: REQUEST_API_MAP.init, data, config });

/** @type {function(import('./api.d.ts').UpgradeBattlePassReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').UpgradeBattlePassResp},any]>} */
export const upgradeBattlePass = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.upgradeBattlePass, data, config });

/** @type {function(import('./api.d.ts').ReceiveRewardReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').ReceiveRewardResp},any]>} */
export const receiveReward = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.receiveReward, data, config });

/** @type {function(import('./api.d.ts').ReceiveAllRewardReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').ReceiveAllRewardResp},any]>} */
export const receiveAll = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.receiveAll, data, config });

/** @type {function(import('./api.d.ts').BattlePassProcessReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').BattlePassProcessResp},any]>} */
export const battlePassTaskProcess = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.battlePassTaskProcess, data, config });

/** @type {function(import('./api.d.ts').BattlePassReceiveRecordReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').BattlePassReceiveRecordResp},any]>} */
export const battlePassReceiveRecord = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.battlePassReceiveRecord, data, config });

/** @type {function(import('./api.d.ts').LotteryDataReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').LotteryDataResp},any]>} */
export const lotteryData = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.lotteryData, data, config });

/** @type {function(import('./api.d.ts').LotteryReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').LotteryResp},any]>} */
export const lottery = (data, config) => fetchApi({ api: REQUEST_API_MAP.lottery, data, config });

/** @type {function(import('./api.d.ts').LotteryRecordReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').LotteryRecordResp},any]>} */
export const lotteryRecord = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.lotteryRecord, data, config });

/** @type {function(import('./api.d.ts').GetUserTbeanReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').GetUserTbeanResp},any]>} */
export const getUserTbean = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getUserTbean, data, config });

/** @type {function(import('./api.d.ts').BuyRiskControlReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').RiskControlRes},any]>} */
export const buyRiskControl = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.buyRiskControl, data, config });

/** @type {function(import('./api.d.ts').OpenTreasureBoxReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').OpenTreasureBoxResp},any]>} */
export const openTreasureBox = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.openTreasureBox, data, config });

/** @type {function(import('./api.d.ts').GiftRankReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').GiftRankResp},any]>} */
export const giftRank = (data, config) => fetchApi({ api: REQUEST_API_MAP.giftRank, data, config });

/** @type {function(import('./api.d.ts').ExchangeRewardReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').ExchangeRewardResp},any]>} */
export const exchangeReward = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.exchangeReward, data, config });

/** @type {function(import('./api.d.ts').BuyLevelReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').BuyLevelResp},any]>} */
export const buyLevel = (data, config) => fetchApi({ api: REQUEST_API_MAP.buyLevel, data, config });

/** @type {function(import('./api.d.ts').GetCidsReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').GetCidsResp},any]>} */
export const getCids = (data, config) => fetchApi({ api: REQUEST_API_MAP.getCids, data, config });

export default {
    init,
    upgradeBattlePass,
    receiveReward,
    receiveAll,
    battlePassTaskProcess,
    battlePassReceiveRecord,
    lotteryData,
    lottery,
    lotteryRecord,
    getUserTbean,
    buyRiskControl,
    openTreasureBox,
    giftRank,
    exchangeReward,
    buyLevel,
    getCids,
};
