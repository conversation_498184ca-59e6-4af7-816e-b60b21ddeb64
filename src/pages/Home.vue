<route lang="json">
    {
        "name": "Home",
        "path": "/:pathMatch(.*)*",
        "meta": {
            "title": "海洋之约 TT通行证"
        }
    }
    </route>

<template>
    <page-container>
        <div class="home">
            <div
                class="home-banner"
                :class="{ short: tabStore.isHalfScreen }">
                <home-top-bar />
                <home-global-level
                    v-if="initStore.inited"
                    class="absolute bottom-[-16.5px] left-[0]" />
            </div>
            <home-tabs
                v-if="initStore.inited"
                class="mx-auto mt-[32.5px]" />
            <div
                v-if="initStore.inited"
                class="home-content mt-[12px]">
                <tab1 v-if="tabStore.currentTabId === TABS.TAB1 && initStore.canRender" />
                <tab2 v-if="tabStore.currentTabId === TABS.TAB2" />
                <tab3 v-if="tabStore.currentTabId === TABS.TAB3" />
                <tab4 v-if="tabStore.currentTabId === TABS.TAB4" />
            </div>
        </div>
        <modal-buy-level />
        <modal-buy-battle-pass />
        <modal-buy-battle-pass-result />
        <modal-no-enough-bean />
        <modal-level-reward-details />
        <modal-take-one-level-rewards-result />
        <modal-take-all-rewards-result />
        <modal-exp-box-result />
        <draw-record-modal></draw-record-modal>
        <exchange-confirm-modal></exchange-confirm-modal>
    </page-container>
</template>

<script setup name="Home">
import useInitStore from '@/stores/modules/use-init-store';
import useTabStore, { TABS } from '@/stores/modules/use-tab-store';
import useLoading from '@/hooks/use-loading';

const initStore = useInitStore();
const tabStore = useTabStore();

useLoading(computed(() => initStore.loading.init));
onMounted(init);
watch(() => tabStore.currentTabId, initStore.refresh);

async function init() {
    await initStore.init();
    if (!initStore.inited) {
        showToast('网络错误，请刷新页面重试');
        return;
    }
    if (myWebview.params.bpmodal) {
        useEventBus('modal-buy-battle-pass').emit({ show: true });
    }
}
</script>

<style lang="less" scoped>
.home {
    position: relative;
    width: 100%;
    min-height: 100%;
    overflow-x: hidden;
    background-color: #072466;
    .home-banner {
        .pic-bg(url('images/<EMAIL>'), 375px, 287.5px);
        position: relative;
        &.short {
            padding-top: 17px;
            background-image: none;
            height: 100px;
            font-size: 28px;
            font-weight: 700;
            text-align: center;
            color: #ffffff;
            line-height: 15px;
            letter-spacing: -0.56px;
        }
    }
    .home-content {
        width: 375px;
        overflow-x: hidden;
    }
}
</style>
