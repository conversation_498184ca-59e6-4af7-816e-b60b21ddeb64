import { cloneDeep } from 'lodash-es';
import { rewardMap } from '@/config/reward';
import { omitValue } from '@/utils';

export const BATTLE_PASS_EXP_REWARD = {
    resource_id: 'BATTLE_PASS_EXP_REWARD',
    name: '通行证经验值',
    url: getImgUrl('<EMAIL>')
}

export const BASE_EXP_REWARD = {
    resource_id: 'BATTLE_PASS_EXP_REWARD',
    name: '进阶经验值',
    url: getImgUrl('<EMAIL>')
}

export function getRewardImgUrl(value) {
    return new URL(`../assets/img/rewards/${value}`, import.meta.url).href;
}

export function getImgUrl(value) {
    return new URL(`../assets/img/${value}`, import.meta.url).href;
}

function getMark({ info, originPrice, originPriceText }) {
    if (!originPrice) {
        return info.mark;
    }
    // v1
    if (info.package_type) {
        if (info.package_type.includes('豆豆礼物')) {
            return `${originPriceText}豆礼物`;
        }
        if (info.package_type.includes('红钻礼物')) {
            return `${originPriceText}红钻礼物`;
        }
    }
    // v2
    if (Array.isArray(info.special_type)) {
        if (info.special_type.includes('豆豆礼物')) {
            return `${originPriceText}豆礼物`;
        }
        if (info.special_type.includes('红钻礼物')) {
            return `${originPriceText}红钻礼物`;
        }
    }
    else if (typeof info.special_type === 'string') {
        if (info.special_type.includes('豆豆礼物')) {
            return `${originPriceText}豆礼物`;
        }
        if (info.special_type.includes('红钻礼物')) {
            return `${originPriceText}红钻礼物`;
        }
    }
    return info.mark;
}

export function handleRewardId(id) {
    const reward = cloneDeep(rewardMap[id] || null);
    return handleReward(reward);
}

export function handleReward(reward) {
    if (reward?.resource_id && rewardMap[reward.resource_id]) {
        const info = JSON.parse(JSON.stringify(rewardMap[reward.resource_id]));
        const originPrice = (+((info.package_price || info.price || 0)
            .toString()
            .replace('T豆', '')
            .replace('豆豆', '')
            .replace('豆', ''))
            .replace('红钻', '')) || 0;
        const originPriceText = omitValue(originPrice);
        const mark = getMark({ info, originPrice, originPriceText });
        let url = '';
        try {
            url = getRewardImgUrl(info.image);
        }
        catch {
            url = '';
        }
        return {
            ...info,
            ...reward,
            mark,
            url,
            originPrice,
            originPriceText,
        };
    }
    return reward;
}
