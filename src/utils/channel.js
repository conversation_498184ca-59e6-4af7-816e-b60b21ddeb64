// '{"channel_id":2042869,"display_id":2181085,"name":"语音直播房","type":7,"bind_id":223072214,"creator_uid":2413687}'
export function getCurrentChannelInfo() {
    let channelInfo = {};
    try {
        if (myWebview.isInApp()) {
            const ci = TTJSBridge.invoke('data', 'getCurrentChannelInfo') || '';
            channelInfo = JSON.parse(ci);
        }
        else {
            channelInfo = {};
        }
    }
    catch {
        channelInfo = {};
    }
    return channelInfo;
}

export function getCurrentChannelId() {
    return getCurrentChannelInfo()?.channel_id;
}
