export const convertVw = (value) => {
  return `${value / 3.75}vw`;
};

export function decodeBase64(base64String) {
  // 去除 Base64 字符串中的空格和换行符
  base64String = base64String.replace(/[^A-Z0-9+/=]/gi, '');

  // 将 Base64 字符串解码为字节数组
  const binaryString = atob(base64String);
  const byteArray = new Uint8Array(binaryString.length);

  // 将解码的二进制字符串转换为字节数组
  for (let i = 0; i < binaryString.length; i++) {
      byteArray[i] = binaryString.charCodeAt(i);
  }

  // 使用 TextDecoder 来正确地解码 UTF-8 编码的字节数组
  const decoder = new TextDecoder('utf-8');
  return decoder.decode(byteArray);
}

export function getRandomItem(arr) {
  if (!Array.isArray(arr) || arr.length === 0) {
      return undefined;
  }
  const index = Math.floor(Math.random() * arr.length);
  return arr[index];
}