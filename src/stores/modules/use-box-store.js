import { defineStore } from 'pinia';
import useInitStore from './use-init-store';
import { requestAnimationFrameFn } from '@/utils';
import { getNow, getWeekdayZhNum } from '@/utils/time';
import { openTreasureBox } from '@/api';

const [rAF, cRAF] = requestAnimationFrameFn();

const useBoxStore = defineStore('box', () => {
    const initStore = useInitStore();
    const battlePassExtraExp = computed(() => initStore.initData.openBoxExtraExp || 0);
    const hasOpenBox = computed(() => initStore.initData.hasOpenBox);
    const loading = ref({ take: false });
    const canTakeBox = ref(false);
    const timer = ref(0);

    function roll() {
        const now = getNow();
        canTakeBox.value
            = now >= window.activityStartTimeStamp
            && now <= window.activityEndTimeStamp
            && (!hasOpenBox.value || now >= initStore.initData.nextCanOpenTime);
        timer.value = rAF(roll);
    }

    function cancelRoll() {
        if (timer.value) {
            cRAF(timer.value);
            timer.value = 0;
        }
    }

    async function takeBox() {
        if (loading.value.take) {
            return;
        }
        if (!canTakeBox.value) {
            const { zh } = getWeekdayZhNum(initStore.initData.nextCanOpenTime);
            showToast(`下周${zh}再来吧`);
            return;
        }
        loading.value.take = true;
        const [res] = await openTreasureBox();
        if (res?.code === 0 && res?.data?.exp) {
            await initStore.init();
            useEventBus('modal-exp-box-result').emit({
                show: true,
                exp: res?.data?.exp,
                extraExp: res?.data?.extraExp || 0,
            });
        }
        loading.value.take = false;
    }

    return {
        hasOpenBox,
        loading,
        canTakeBox,
        battlePassExtraExp,
        roll,
        cancelRoll,
        takeBox,
    };
});

export default useBoxStore;
