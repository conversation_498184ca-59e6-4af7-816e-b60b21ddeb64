import { defineStore } from 'pinia';
import { battlePassTaskProcess, getCids } from '@/api';
import { getRandomItem } from '@/utils/others';
import { toRoom } from '@/utils/jsbridge';
import { getTimeStampDetails } from '@/utils/time';

export const TABS = {
    DAILY: 'daily',
    WEEKLY: 'weekly',
    PERIODLY: 'periodly ',
};

export const TABS_TEXT = {
    [TABS.DAILY]: '每日任务',
    [TABS.WEEKLY]: '每周任务',
    [TABS.PERIODLY]: '每期任务',
};

export const TASKS_TYPE = {
    DAILY: 1,
    WEEKLY: 2,
    PERIODLY: 3,
    LIMITED: 5,
};

export const TABS_TASKS_TYPES = {
    [TABS.DAILY]: [TASKS_TYPE.DAILY],
    [TABS.WEEKLY]: [TASKS_TYPE.WEEKLY],
    [TABS.PERIODLY]: [TASKS_TYPE.PERIODLY, TASKS_TYPE.LIMITED],
};

export const TASKS_FUNC = {
    ROOM: 'room',
    BATTLEPASS: 'battlepass',
};

export const TASKS_SHOW_TYPE = {
    LONG_PROGRESS_BAR: 'long-progress-bar',
    PERCENT: 'percent',
    SHORT_PROGRESS_BAR_PERCENT: 'short-progress-bar-percent',
    NONE: 'none',
};

export const TASKS_LIST = {
    [TASKS_TYPE.DAILY]: [
        {
            title: '每日签到',
            showType: TASKS_SHOW_TYPE.PERCENT,
        },
        {
            title: '娱乐房间停留120S',
            showType: TASKS_SHOW_TYPE.PERCENT,
            unit: '次',
            funcName: TASKS_FUNC.ROOM,
        },
        {
            title: '送任意豆豆礼物',
            showType: TASKS_SHOW_TYPE.PERCENT,
            funcName: TASKS_FUNC.ROOM,
        },
        {
            title: '送出豆豆礼物金额到达',
            showType: TASKS_SHOW_TYPE.LONG_PROGRESS_BAR,
            funcName: TASKS_FUNC.ROOM,
        },
    ],
    [TASKS_TYPE.WEEKLY]: [
        {
            title: '累计签到5天',
            showType: TASKS_SHOW_TYPE.SHORT_PROGRESS_BAR_PERCENT,
        },
        {
            title: '累计消费300000豆',
            showType: TASKS_SHOW_TYPE.SHORT_PROGRESS_BAR_PERCENT,
            funcName: TASKS_FUNC.ROOM,
        },
        {
            title: '打赏指定礼物10个',
            showType: TASKS_SHOW_TYPE.SHORT_PROGRESS_BAR_PERCENT,
            funcName: TASKS_FUNC.ROOM,
        },
    ],
    [TASKS_TYPE.PERIODLY]: [
        {
            title: '累计签到30天',
            showType: TASKS_SHOW_TYPE.SHORT_PROGRESS_BAR_PERCENT,
        },
        {
            title: '累计消费500000豆',
            showType: TASKS_SHOW_TYPE.SHORT_PROGRESS_BAR_PERCENT,
            funcName: TASKS_FUNC.ROOM,
        },
        {
            title: '打赏指定礼物10个',
            showType: TASKS_SHOW_TYPE.SHORT_PROGRESS_BAR_PERCENT,
            funcName: TASKS_FUNC.ROOM,
        },
        {
            title: '完成豪华进阶',
            showType: TASKS_SHOW_TYPE.NONE,
            funcName: TASKS_FUNC.BATTLEPASS,
        },
    ],
    [TASKS_TYPE.LIMITED]: [
        {
            title: '打赏指定礼物10个',
            showType: TASKS_SHOW_TYPE.SHORT_PROGRESS_BAR_PERCENT,
            funcName: TASKS_FUNC.ROOM,
        },
    ],
};

const useTasksStore = defineStore('tasks', () => {
    const loading = ref({ tasks: false, room: false });
    const currentTasksTab = ref(TABS.DAILY);
    const tasks = ref([]);
    const tabs = ref(
        [TABS.DAILY, TABS.WEEKLY, TABS.PERIODLY].map(tab => ({
            tab,
            text: TABS_TEXT[tab],
            types: TABS_TASKS_TYPES[tab],
        })),
    );

    const TASKS_FUNCTIONS = {
        [TASKS_FUNC.ROOM]: async () => {
            if (loading.value.room) {
                return;
            }
            loading.value.room = true;
            const [res] = await getCids();
            const cid = getRandomItem(res?.data?.cids || []);
            console.log('任务进房：', cid);
            if (cid) {
                toRoom(cid);
            }
            else {
                showToast('暂无房间');
            }
            loading.value.room = false;
        },
        [TASKS_FUNC.BATTLEPASS]: () => {
            useEventBus('modal-buy-battle-pass').emit({ show: true });
        },
    };

    function setCurrentTasksTab(tab) {
        if (Object.values(TABS).includes(tab) && currentTasksTab.value !== tab) {
            currentTasksTab.value = tab;
        }
    }

    async function queryTasks() {
        if (loading.value.tasks) {
            return;
        }
        loading.value.tasks = true;
        tasks.value = [];
        const tasksTypes = TABS_TASKS_TYPES[currentTasksTab.value] || [];
        const defaultTasks = tasksTypes.reduce((_, taskType) => {
            Array.prototype.push.apply(_, TASKS_LIST[taskType]);
            return _;
        }, []);
        if (tasksTypes.length) {
            const allResArr = await Promise.all(tasksTypes.map(taskType => battlePassTaskProcess({ type: taskType })));
            if (allResArr.every(resArr => resArr?.[0]?.code === 0)) {
                const backendTasks = allResArr.reduce((_, [res]) => {
                    Array.prototype.push.apply(_, res?.data?.list || []);
                    return _;
                }, []);
                const realTasks = [];
                for (const [index, backendTask] of backendTasks.entries()) {
                    const defaultTask = defaultTasks[index] || {};
                    const func = TASKS_FUNCTIONS[defaultTask.funcName] || null;
                    const status = backendTask.stages?.length
                        ? backendTask.stages.every(stage => !!stage.status)
                        : backendTask.process >= backendTask.total;
                    const startTimeDetails = getTimeStampDetails(backendTask.startTime || 0);
                    const endTimeDetails = getTimeStampDetails(backendTask.endTime || 0);
                    const timeText = backendTask.startTime && backendTask.endTime
                        ? `${startTimeDetails.month}月${startTimeDetails.day}日${startTimeDetails.hourText}:${startTimeDetails.minuteText}-${endTimeDetails.month}月${endTimeDetails.day}日${endTimeDetails.hourText}:${endTimeDetails.minuteText}`
                        : '';
                    realTasks.push({ ...backendTask, ...defaultTask, status, timeText, func });
                }
                tasks.value = realTasks;
            }
            else {
                showToast('网络错误请重试');
            }
        }
        console.log('tasks: ', tasks.value);
        loading.value.tasks = false;
    }

    return {
        loading,
        tasks,
        tabs,
        currentTasksTab,
        setCurrentTasksTab,
        queryTasks,
    };
});

export default useTasksStore;
