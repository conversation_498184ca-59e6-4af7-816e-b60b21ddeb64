import { defineStore } from 'pinia';
import useInitStore from './use-init-store';
import useBattlePassStore, { BATTLE_PASS_TYPE } from './use-battle-pass-store';
import { battlePassReceiveRecord } from '@/api';
import { handleRewardId } from '@/utils/reward';

const useLevelStore = defineStore('level', () => {
    const initStore = useInitStore();
    const battlePassStore = useBattlePassStore();

    const loading = ref({ details: false });
    const levelsRewards = ref([]);

    const maxLevel = computed(() => initStore.initData.maxLevel || 0); // 可以达到的最大等级
    const everyLevelExp = computed(() => initStore.initData.everyLimitExp || 0); // 每一级所需经验
    const everyLevelPrize = computed(() => initStore.initData.everyLevelPrize || 0); // 每升一级所需豆豆数
    const currentExp = computed(() => initStore.initData.exp || 0); // 当前总经验值
    const currentLevel = computed(() => Math.min(Number.parseInt(currentExp.value / everyLevelExp.value), maxLevel.value)); // 当前等级
    const isReachMaxLevel = computed(() => currentLevel.value >= maxLevel.value); // 是否已达最高等级
    const everyPeriodBuyLevelLimitCount = computed(() => initStore.initData.buyLevelLimit || 0); // 每期可购买的等级级数上限
    const hasBuyLevelCount = computed(() => initStore.initData.hasBuyLevel || 0); // 已经购买的等级级数
    const canBuyLevelCount = computed(() => Math.max(0, Math.min(everyPeriodBuyLevelLimitCount.value - hasBuyLevelCount.value, maxLevel.value - currentLevel.value))); // 还能购买的等级级数
    const closestBigLevel = computed(() => (Number.parseInt(currentLevel.value / 10) * 10) + 10); // 下一个大等级（每10级）
    const closestBigLevelRewards = computed(() => (isReachMaxLevel.value ? [] : levelsRewards.value[closestBigLevel.value]) || []); // 下一个大等级的奖励

    // 所有的还未领取的礼物，包括可以领取的礼物 和 还不可以领取的礼物
    const unTakenRewards = computed(() => {
        const result = { normal: [], advanced: [], super: [] };
        for (const row of levelsRewards.value) {
            Array.prototype.push.apply(result.normal, row.normal.filter(reward => !reward.status));
            Array.prototype.push.apply(result.advanced, row.advanced.filter(reward => !reward.status));
            Array.prototype.push.apply(result.super, row.super.filter(reward => !reward.status));
        }
        return result;
    });

    // 可以领取的暂未领取的礼物
    const canTakeRewards = computed(() => {
        const result = { normal: [], advanced: [], super: [] };
        Array.prototype.push.apply(result.normal, unTakenRewards.value.normal.filter(reward => currentLevel.value >= reward.level));
        Array.prototype.push.apply(result.advanced, unTakenRewards.value.advanced.filter(reward => battlePassStore.hasBuyAdvancedBattlePass && currentLevel.value >= reward.level));
        Array.prototype.push.apply(result.super, unTakenRewards.value.super.filter(reward => battlePassStore.hasBuySuperBattlePass && currentLevel.value >= reward.level));
        return result;
    });

    // 是否已经领取完所有礼物
    const hasTokeAllRewards = computed(() => {
        if (!isReachMaxLevel.value || !battlePassStore.hasBuyAllBattlePass) {
            return false;
        }
        for (const row of levelsRewards.value) {
            const tokeAllNormalRewards = row.normal.every(reward => reward.status);
            const tokeAllAdvancedRewards = row.advanced.every(reward => reward.status);
            const tokeAllSuperRewards = row.super.every(reward => reward.status);
            if (!tokeAllNormalRewards || !tokeAllAdvancedRewards || !tokeAllSuperRewards) {
                return false;
            }
        }
        return true;
    });

    async function queryLevelsDetails() {
        loading.value.details = true;
        const [res] = await battlePassReceiveRecord();
        if (res?.code === 0) {
            const allRewards = res?.data?.allRewards || {};
            const recordsMap = (res?.data?.records || []).reduce((_, record) => {
                if (!_[record.level]) {
                    _[record.level] = 0;
                }
                _[record.level] = Math.max(_[record.level], record.type);
                return _;
            }, {});
            const create = (rewards, level, battlePassType) => {
                return rewards.reduce((_, reward) => {
                    const details = handleRewardId(reward.id);
                    if (details) {
                        _.push({
                            ...reward,
                            ...details,
                            level,
                            battlePassType,
                            status: (recordsMap[level] || 0) >= battlePassType,
                        });
                    }
                    return _;
                }, []);
            };
            // 包含0级
            levelsRewards.value = Array.from({ length: maxLevel.value + 1 })
                .fill(null)
                .map((_, index) => {
                    const level = index;
                    return {
                        level,
                        normal: create(allRewards.normalCard[level]?.rewards, level, BATTLE_PASS_TYPE.NORMAL),
                        advanced: create(allRewards.advancedCard[level]?.rewards, level, BATTLE_PASS_TYPE.ADVANCED),
                        super: create(allRewards.superCard[level]?.rewards, level, BATTLE_PASS_TYPE.SUPER),
                    };
                });
            console.log('levelsRewards: ', levelsRewards.value);
        }
        else {
            levelsRewards.value = [];
            showToast('网络错误，请刷新页面重试');
        }
        loading.value.details = false;
    }

    return {
        loading,
        levelsRewards,
        everyLevelExp,
        everyLevelPrize,
        currentExp,
        currentLevel,
        isReachMaxLevel,
        everyPeriodBuyLevelLimitCount,
        hasBuyLevelCount,
        canBuyLevelCount,
        closestBigLevel,
        closestBigLevelRewards,
        unTakenRewards,
        canTakeRewards,
        hasTokeAllRewards,
        queryLevelsDetails,
    };
});

export default useLevelStore;
