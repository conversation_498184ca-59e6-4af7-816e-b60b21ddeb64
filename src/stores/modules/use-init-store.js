import { defineStore } from 'pinia';
import { init as _init } from '@/api';

const useInitStore = defineStore('init', () => {
    const inited = ref(false);
    const canRender = ref(true);
    const loading = ref({ init: false });
    const state = reactive({
        initData: {},
        serverTime: 0,
    });

    const isEnd = computed(() => {
        const { endTime } = state.initData;
        return state.serverTime >= endTime;
    });

    let countTimeKey = null;

    // 模拟服务器时间
    function countServerTime(serverTime) {
        if (countTimeKey) {
            clearInterval(countTimeKey);
            countTimeKey = null;
        }
        window.serverTime = serverTime;
        state.serverTime = serverTime;
        countTimeKey = setInterval(() => {
            window.serverTime += 1;
            state.serverTime += 1;
        }, 1000);
    }

    async function init(payload = {}) {
        loading.value.init = true;
        const [res] = await _init(payload);
        if (res?.code === 0) {
            state.initData = res?.data || {};
            countServerTime(res?.data?.serverTime || Number.parseInt(Date.now() / 1000));
            window.activityStartTimeStamp = res?.data?.startTime || res?.data?.beginTime || 0;
            window.activityEndTimeStamp = res?.data?.endTime || 0;
            inited.value = true;
            loading.value.init = false;
            return true;
        }
        loading.value.init = false;
        return false;
    };

    async function refresh() {
        canRender.value = false;
        await init();
        canRender.value = true;
    }

    return {
        ...toRefs(state),
        init,
        refresh,
        canRender,
        loading,
        inited,
        isEnd,
    };
});

export default useInitStore;
