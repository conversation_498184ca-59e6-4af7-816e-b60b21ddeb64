import { defineStore } from 'pinia';
import { jumpBelugaLink } from '@/utils/index';
import { finishActivity, onBackPressed } from '@/utils/jsbridge';

const useRuleStore = defineStore('rule', () => {
    const $route = useRoute();

    function linkToRule() {
        jumpBelugaLink('');
    }

    function back() {
        if ($route.name === 'Home') {
            finishActivity();
        }
        else {
            onBackPressed();
        }
    }

    return {
        back,
        linkToRule,
    };
});

export default useRuleStore;
