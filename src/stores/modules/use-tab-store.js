import { defineStore } from 'pinia';

export const TABS = {
    TAB1: 1,
    TAB2: 2,
    TAB3: 3,
    TAB4: 4,
};

export const TABS_TEXT = {
    [TABS.TAB1]: '奖励',
    [TABS.TAB2]: '任务',
    [TABS.TAB3]: '商城',
    [TABS.TAB4]: '榜单',
};

function getMountedTabId() {
    const { tab } = myWebview.params || {};
    return Object.values(TABS).includes(+tab) ? +tab : TABS.TAB1;
}

const useTabStore = defineStore('tab', () => {
    const isHalfScreen = computed(() => !!myWebview.params.halfScreen);
    const currentTabId = ref(getMountedTabId());
    const tabs = ref([
        { id: TABS.TAB1, text: TABS_TEXT[TABS.TAB1] },
        { id: TABS.TAB2, text: TABS_TEXT[TABS.TAB2] },
        { id: TABS.TAB3, text: TABS_TEXT[TABS.TAB3] },
        { id: TABS.TAB4, text: TABS_TEXT[TABS.TAB4] },
    ]);

    async function setTabId(id) {
        if (Object.values(TABS).includes(id) && currentTabId.value !== id) {
            currentTabId.value = id;
        }
    }

    return {
        isHalfScreen,
        tabs,
        currentTabId,
        setTabId,
    };
});

export default useTabStore;
