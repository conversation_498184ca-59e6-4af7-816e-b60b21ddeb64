import { defineStore } from 'pinia';
import useInitStore from './use-init-store';

export const BATTLE_PASS_TYPE = {
    NORMAL: 1,
    ADVANCED: 2,
    SUPER: 3,
};

export const BATTLE_PASS_TYPE_NAME = {
    [BATTLE_PASS_TYPE.NORMAL]: '基础',
    [BATTLE_PASS_TYPE.ADVANCED]: '进阶',
    [BATTLE_PASS_TYPE.SUPER]: '豪华',
};

export const BATTLE_PASS_TYPE_COLS = [
    { battlePassType: BATTLE_PASS_TYPE.NORMAL, key: 'normal', title: BATTLE_PASS_TYPE_NAME[BATTLE_PASS_TYPE.NORMAL] },
    { battlePassType: BATTLE_PASS_TYPE.ADVANCED, key: 'advanced', title: BATTLE_PASS_TYPE_NAME[BATTLE_PASS_TYPE.ADVANCED] },
    { battlePassType: BATTLE_PASS_TYPE.SUPER, key: 'super', title: BATTLE_PASS_TYPE_NAME[BATTLE_PASS_TYPE.SUPER] },
];

const useBattlePassStore = defineStore('battle-pass', () => {
    const initStore = useInitStore();
    const hasBuyAdvancedBattlePass = computed(() => [BATTLE_PASS_TYPE.ADVANCED, BATTLE_PASS_TYPE.SUPER].includes(initStore.initData.battlePassType)); // 已普通进阶
    const hasBuySuperBattlePass = computed(() => [BATTLE_PASS_TYPE.SUPER].includes(initStore.initData.battlePassType)); // 已豪华进阶
    const hasBuyAllBattlePass = computed(() => hasBuyAdvancedBattlePass.value && hasBuySuperBattlePass.value); // 已全部进阶
    const advancedBattlePassPrice = computed(() => initStore.initData.advancedBattlePassPrice || 0); // 普通进阶价钱
    const superBattlePassPrice = computed(() =>
        hasBuyAdvancedBattlePass.value
            ? (initStore.initData.superBattlePassPrice || 0) - advancedBattlePassPrice.value
            : (initStore.initData.superBattlePassPrice || 0),
    ); // 豪华进阶价钱，如果已普通进阶则只需升级价钱

    return {
        advancedBattlePassPrice,
        superBattlePassPrice,
        hasBuyAdvancedBattlePass,
        hasBuySuperBattlePass,
        hasBuyAllBattlePass,
    };
});

export default useBattlePassStore;
