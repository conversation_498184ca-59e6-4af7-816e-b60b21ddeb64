import { showConfirmDialog, showToast } from 'vant';
import FaceR<PERSON>ognition from '@tt/face-recognition';
import { defineStore } from 'pinia';
import useInitStore from './use-init-store';
import { faceUrl, isPCTT, jsBridgePreview } from '@/config/url';
import { getDeviceId, getVersion, jumpLink, pcRisk } from '@/utils/jsbridge';
import { convertAppVersion } from '@/utils';
import { getCurrentChannelInfo } from '@/utils/channel';
import { CLIENT_TYPE } from '@/const';
import { buyRiskControl, getUserTbean } from '@/api';
import { decodeBase64 } from '@/utils/others';
import { to } from '@/auto-imports/utils';

export const RISK_TYPE = {
    ADVANCED_BATTLE_PASS: 1,
    SUPER_BATTLE_PASS: 2,
    LEVEL: 3,
};

// 获取点版本号 x.x.x
export function getVersionDotText() {
    try {
        const originVersion = getVersion().toString();
        if (isPCTT) {
            return window.atob(originVersion);
        }
        else {
            if (/\d+\.\d+\.\d+/.test(originVersion)) {
                return originVersion;
            }
            else if (/\d+/.test(originVersion)) {
                return convertAppVersion(+originVersion || 0, 'string');
            }
            else {
                return '';
            }
        }
    }
    catch {
        return '';
    }
}

// 获取数字版本号
export function getVersionNumber() {
    try {
        const originVersion = getVersion().toString();
        if (isPCTT) {
            return convertAppVersion(window.atob(originVersion), 'number');
        }
        else {
            if (/\d+\.\d+\.\d+/.test(originVersion)) {
                return convertAppVersion(originVersion, 'number');
            }
            else if (/\d+/.test(originVersion)) {
                return +originVersion || 0;
            }
            else {
                return 0;
            }
        }
    }
    catch {
        return 0;
    }
}

// API风控参数
export function getRiskControlParams() {
    const channelInfo = getCurrentChannelInfo();
    const marketId = myWebview.params.market_id || 0;
    const roomId = channelInfo?.channel_id || 0;
    const roomType = channelInfo?.type || 0;
    const deviceId = getDeviceId() || '';
    const ip = myWebview.params.ip || '';
    return {
        deviceId,
        ip,
        marketId,
        clientType: CLIENT_TYPE,
        appVersion: getVersionDotText(),
        appVersionCode: getVersionNumber(),
        ...(roomId ? { roomId } : {}),
        ...(roomType ? { roomType } : {}),
    };
}

const useRiskStore = defineStore('risk', () => {
    const initStore = useInitStore();
    const loading = ref({ risk: false, bean: false });

    async function faceRisk(face_auth_context_json) {
        try {
            console.log('开始人脸识别，face_auth_context_json: ', face_auth_context_json);
            const face = new FaceRecognition({ host: faceUrl });
            const res = await face.startVerify({ context: face_auth_context_json, scene_id: '13' });
            return res.result_token;
        }
        catch (error) {
            console.error('人脸识别失败：', error);
            return '';
        }
    }

    async function risk(type, riskParams = {}) {
        const [res, err] = await buyRiskControl({
            ...getRiskControlParams(),
            ...riskParams,
            type, // 1:超值通行证 2:豪华通行证 3:买等级
        });
        if (!err && res?.code === 0) {
            // 人脸就返回 -12351，实名返回 -12352，拒绝消费返回 -4504， 弹窗验证 返回 -185
            const errorCode = res?.data?.errCode;
            if (typeof errorCode !== 'number') {
                return false;
            }
            if (errorCode === 0) {
                return true;
            }
            else {
                if (errorCode === -12352) {
                    const [dialogRes] = await to(
                        showConfirmDialog({
                            title: '温馨提示',
                            message: '为了保障您的财产安全，请到最新版本完成实名认证和人脸识别认证',
                            showConfirmButton: true,
                            showCancelButton: true,
                            cancelButtonText: '关闭',
                            confirmButtonText: '去完成',
                        }),
                    );
                    if (dialogRes === 'confirm') {
                        jumpLink(`${jsBridgePreview}/setting?back_to_finish=true`);
                    }
                    return false;
                }
                else if (errorCode === -12351) {
                    console.log('风控errInfo: ', res?.data?.errInfo);
                    const [dialogRes] = await to(
                        showConfirmDialog({
                            title: '温馨提示',
                            message: '请先进行人脸识别验证，验证通过后即可继续操作',
                            showConfirmButton: true,
                            showCancelButton: true,
                            confirmButtonText: '开始人脸识别',
                            cancelButtonText: '取消',
                        }),
                    );
                    if (dialogRes === 'confirm') {
                        let face_auth_context_json = '';
                        try {
                            face_auth_context_json = decodeBase64(res?.data?.errInfo);
                        }
                        catch {
                            face_auth_context_json = '';
                        }
                        const faceToken = await faceRisk(face_auth_context_json);
                        console.log('faceToken: ', faceToken);
                        if (faceToken) {
                            const riskResult = await risk(type, { faceToken });
                            return riskResult;
                        }
                        else {
                            showToast('人脸识别失败，请重试');
                            return false;
                        }
                    }
                    else {
                        return false;
                    }
                }
                else if (errorCode === -185) {
                    let info = {};
                    try {
                        info = JSON.parse(decodeBase64(res?.data?.errInfo));
                        info.risk_token = info?.apply_key || '';
                        console.log('解析后内容：', info);
                    }
                    catch {
                        info = {};
                    }
                    const { isSuccess, risk_token } = await pcRisk(info);
                    if (+isSuccess && risk_token) {
                        const riskResult = await risk(type, { pcToken: risk_token });
                        return riskResult;
                    }
                    else {
                        showToast('暂无法购买，请前往手机端参与活动～');
                        return false;
                    }
                }
                else {
                    showToast(res?.data?.errMsg || '拒绝消费');
                    return false;
                }
            }
        }
        else {
            return false;
        }
    }

    async function checkRiskSuccess(riskType) {
        if (!initStore.initData.openRisk) {
            return true;
        }
        loading.value.risk = true;
        const riskSuccss = await risk(riskType);
        loading.value.risk = false;
        return riskSuccss;
    }

    async function checkBeanEnough(price = 0) {
        loading.value.bean = true;
        const [res] = await getUserTbean();
        const enoughBean = (res?.data?.tbean || 0) >= price;
        console.log('需要的豆数：', price);
        console.log('现在的豆数：', res?.data?.tbean);
        loading.value.bean = false;
        if (!enoughBean) {
            useEventBus('modal-no-enough-bean').emit({ show: true });
        }
        return enoughBean;
    }

    return {
        loading,
        checkRiskSuccess,
        checkBeanEnough,
    };
});

export default useRiskStore;
