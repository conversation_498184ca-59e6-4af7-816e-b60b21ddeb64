<template>
    <div
        class="tab1"
        :style="fillStyle">
        <tab1-level-list />
        <common-copyright class="mt-[15px]" />
        <div
            ref="$observeTarget"
            class="footer">
            <tab1-next-big-level v-if="hasNextBigLevel" />
            <div class="actions">
                <div
                    class="btn take"
                    @click="takeAllRewards">
                    {{ levelStore.hasTokeAllRewards ? '奖励已领完' : '一键领取' }}
                </div>
                <div
                    v-if="!battlePassStore.hasBuyAllBattlePass"
                    class="btn buy-battle-pass"
                    @click="openBattlePassModal">
                    {{ !battlePassStore.hasBuyAdvancedBattlePass ? '通行证进阶' : '豪华进阶' }}
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { convertVw } from '@/utils/others';
import { receiveAll } from '@/api';
import { handleRewardId } from '@/utils/reward';
import { sleep } from '@/utils';
import useBattlePassStore from '@/stores/modules/use-battle-pass-store';
import useLevelStore from '@/stores/modules/use-level-store';
import useInitStore from '@/stores/modules/use-init-store';
import useLoading from '@/hooks/use-loading';
import useResizeObserver from '@/hooks/use-resize-observer';

const battlePassStore = useBattlePassStore();
const levelStore = useLevelStore();
const initStore = useInitStore();
const loading = ref({ takeAll: false });
const fillStyle = ref({ paddingBottom: convertVw(220) });
const hasNextBigLevel = computed(() => !levelStore.isReachMaxLevel);
const { $observeTarget, size } = useResizeObserver();

watch(
    () => size.value,
    (val) => {
        if (val.heightVW) {
            fillStyle.value.paddingBottom = `${val.heightVW + 4}vw`;
        }
    },
    { immediate: true },
);

useLoading(computed(() => levelStore.loading.details || loading.value.takeAll));

onMounted(async () => {
    await levelStore.queryLevelsDetails();
    await sleep(300);
    scrollToCurrentLevel();
});

function openBattlePassModal() {
    useEventBus('modal-buy-battle-pass').emit({ show: true });
}
function scrollToCurrentLevel() {
    const $list = document.getElementById('MyLevelList');
    const $current = document.getElementById(`MyLevel${levelStore.currentLevel}`);
    if ($list && $current) {
        $list.scrollIntoView({ behavior: 'smooth', block: 'start', top: 0 });
        $list.scrollTo({
            top: $current.offsetTop - $list.offsetTop,
            behavior: 'smooth',
        });
    }
}

async function takeAllRewards() {
    if (loading.value.takeAll) {
        return;
    }
    loading.value.takeAll = true;
    await initStore.init();
    await levelStore.queryLevelsDetails();
    if (
        levelStore.canTakeRewards.normal.length
        || levelStore.canTakeRewards.advanced.length
        || levelStore.canTakeRewards.super.length
    ) {
        const [res] = await receiveAll();
        await initStore.init();
        await levelStore.queryLevelsDetails();
        if (res?.code === 0 && res?.data?.rewards?.length) {
            const gainRewards = (res?.data?.rewards || []).reduce((_, item) => {
                const details = handleRewardId(item.id);
                if (details) {
                    _.push({ ...item, ...details });
                }
                return _;
            }, []);
            const forwardRewards = [
                ...levelStore.unTakenRewards.super,
                ...levelStore.unTakenRewards.advanced,
            ].filter(reward => reward.level <= levelStore.currentLevel);
            useEventBus('modal-take-all-rewards-result').emit({
                show: true,
                gainRewards,
                forwardRewards,
            });
        }
    }
    else {
        if (!levelStore.isReachMaxLevel) {
            showToast('暂无可领取奖励,快去升级通行证吧~');
        }
        else {
            // 满级却不能点按钮，然而还有没领取的礼物 === 没买完所有通行证
            if (!levelStore.hasTokeAllRewards) {
                showToast('当前奖励已领完,进阶即可解锁更多奖励~');
            }
        }
    }
    loading.value.takeAll = false;
}
</script>

<style lang="less" scoped>
.tab1 {
    position: relative;
    .footer {
        position: fixed;
        left: 0;
        bottom: 0;
        width: 100%;
        background-color: #072466;
        .actions {
            .flex-center();
            height: 80px;
            .btn {
                .flex-center();
                margin-right: 15px;
                &:last-of-type {
                    margin-right: 0;
                }
                &.take {
                    width: 153px;
                    height: 48px;
                    background: #1cbde3;
                    border-radius: 20px;
                }
                &.buy-battle-pass {
                    width: 153px;
                    height: 48px;
                    background: #f1cc28;
                    border-radius: 20px;
                }
            }
        }
    }
}
</style>
