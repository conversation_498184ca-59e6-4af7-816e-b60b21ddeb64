<template>
    <common-modal-container
        v-model:show="isShow"
        :title="title"
        :bg="bg">
        <div
            v-if="isShow"
            class="modal-level-reward-details">
            <common-modal-reward
                v-if="shownReward"
                :reward="shownReward"
                class="reward" />

            <div class="btns">
                <div
                    v-if="showHasKnownButton"
                    class="btn has-known"
                    @click="close">
                    我知道了
                </div>
                <div
                    v-if="showBuyBattlePassButton"
                    class="btn buy-battle-pass"
                    @click="toBuyBattlePass">
                    去进阶
                </div>
            </div>
        </div>
    </common-modal-container>
</template>

<script setup>
import useBattlePassStore, { BATTLE_PASS_TYPE } from '@/stores/modules/use-battle-pass-store';

const battlePassStore = useBattlePassStore();
const bg = ref({ width: 375, height: 355.5, url: requireImg('<EMAIL>') });
const title = ref('进阶豪华通行证即可解锁领取');
const isShow = ref(false);
const shownReward = ref(null);
const showBuyBattlePassButton = ref(false);
const showHasKnownButton = ref(false);

useEventBus('modal-level-reward-details').on(({ show = true, reward }) => {
    isShow.value = show;
    if (isShow.value) {
        shownReward.value = reward;
        if (reward.status) {
            title.value = '奖励详情';
            showHasKnownButton.value = true;
        }
        else {
            if (!battlePassStore.hasBuyAdvancedBattlePass && !battlePassStore.hasBuySuperBattlePass) {
                // 用户没任何进阶
                if (reward.battlePassType === BATTLE_PASS_TYPE.NORMAL) {
                    title.value = '奖励详情';
                    showHasKnownButton.value = true;
                }
                else if (reward.battlePassType === BATTLE_PASS_TYPE.ADVANCED) {
                    title.value = '进阶通行证即可解锁';
                    showBuyBattlePassButton.value = true;
                    showHasKnownButton.value = true;
                }
                else {
                    title.value = '进阶豪华进阶通行证即可解锁';
                    showBuyBattlePassButton.value = true;
                    showHasKnownButton.value = true;
                }
            }
            else if (battlePassStore.hasBuyAdvancedBattlePass && !battlePassStore.hasBuySuperBattlePass) {
                // 用户已普通进阶
                if (reward.battlePassType === BATTLE_PASS_TYPE.NORMAL) {
                    title.value = '奖励详情';
                    showHasKnownButton.value = true;
                }
                else if (reward.battlePassType === BATTLE_PASS_TYPE.ADVANCED) {
                    title.value = '奖励详情';
                    showHasKnownButton.value = true;
                }
                else {
                    title.value = '进阶豪华进阶通行证即可解锁';
                    showBuyBattlePassButton.value = true;
                    showHasKnownButton.value = true;
                }
            }
            else if (battlePassStore.hasBuySuperBattlePass) {
                // 用户已豪华进阶
                title.value = '奖励详情';
                showHasKnownButton.value = true;
            }
            else {
                title.value = '奖励详情';
                showHasKnownButton.value = true;
            }
        }
    }
});

watch(() => isShow.value, (val) => {
    if (!val) {
        setTimeout(() => {
            title.value = '';
            shownReward.value = null;
            showBuyBattlePassButton.value = false;
            showHasKnownButton.value = false;
        }, 300);
    }
});

function toBuyBattlePass() {
    useEventBus('modal-buy-battle-pass').emit({ show: true });
    close();
}

function close() {
    isShow.value = false;
}
</script>

<style lang="less" scoped>
.modal-level-reward-details {
    position: relative;
    .reward {
        .x-center();
        top: 48.75px;
    }
    .btns {
        .x-center();
        .flex-center();
        top: 230px;
        .btn {
            .flex-center();
            margin-right: 20px;
            &:last-of-type {
                margin-right: 0;
            }
            &.has-known {
                width: 125px;
                height: 50px;
                background: linear-gradient(0deg, #209cff 0%, #68e0cf 100%), #f1cc29;
                border: 2px solid #f1e194;
                border-radius: 22px;
            }
            &.buy-battle-pass {
                width: 125px;
                height: 50px;
                background: #f1cc29;
                border: 2px solid #f1e194;
                border-radius: 22px;
            }
        }
    }
}
</style>
