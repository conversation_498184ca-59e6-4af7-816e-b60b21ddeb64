<template>
    <div class="tab1-level-list">
        <div
            id="MyLevelList"
            class="list">
            <div
                v-for="row in shownLevelRewards"
                :id="`MyLevel${row.level}`"
                :key="row.level"
                class="level">
                <div
                    v-if="row.level === levelStore.currentLevel"
                    class="mask"></div>
                <div class="content">
                    <tab1-level-num
                        class="w-[42px]"
                        :level="row.level"
                        :locked="row.level > levelStore.currentLevel" />
                    <tab1-level-row-rewards
                        v-for="col in BATTLE_PASS_TYPE_COLS"
                        :key="`${row.level}-${col.key}`"
                        class="w-[111px]"
                        :rewards="row[col.key]"
                    />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { BATTLE_PASS_TYPE, BATTLE_PASS_TYPE_COLS } from '@/stores/modules/use-battle-pass-store';
import useLevelStore from '@/stores/modules/use-level-store';

const levelStore = useLevelStore();

const shownLevelRewards = computed(() => {
    return levelStore.levelsRewards
        .filter(row => row.level > 0)
        .sort((a, b) => a.level - b.level);
});
</script>

<style lang="less" scoped>
.tab1-level-list {
    .pic-bg(url('images/<EMAIL>'), 375px, 339px);
    display: flex;
    flex-direction: column;
    padding-top: 37.5px;
    .list {
        flex: 1;
        width: 100%;
        overflow-y: scroll;
        overflow-x: hidden;
        .level {
            position: relative;
            .mask {
                position: absolute;
                left: 0;
                top: 0;
                background: rgba(0, 0, 0, 0.3);
                width: 100%;
                height: 100%;
            }
            .content {
                position: relative;
                display: flex;
                align-items: center;
                flex-wrap: nowrap;
                padding: 12.5px 0;
            }
        }
    }
}
</style>
