<template>
    <popup-container
        v-model:show="isShow"
        :close-on-click-overlay="true">
        <div
            v-if="isShow"
            class="modal-buy-battle-pass">
            <div
                class="icon-close"
                @click="close"></div>
            <div class="battle-pass-rewards">
                <div
                    v-for="(reward, index) in BATTLE_PASS_REWARDS"
                    :key="`${reward.resource_id}-${index}`"
                    class="reward"
                    :class="[{ super: reward.super }, `reward-${index + 1}`]">
                    <img :src="reward.url" />
                    <div
                        v-if="reward.super"
                        class="reward-super-tag"></div>
                </div>
            </div>
            <div class="rank-rewards">
                <div
                    v-for="(reward, index) in RANK_REWARDS"
                    :key="`${reward.resource_id}-${index}`"
                    class="reward">
                    <img :src="reward.url" />
                </div>
                <div class="text">
                    购买豪华进阶满级后还可解锁榜单,开启荣
                    <br />
                    耀堂争夺 本期排行榜最终排名还可获得限定
                    <br />
                    座驾+麦位框奖励
                </div>
            </div>
            <div class="battle-pass">
                <div
                    v-if="battlePassStore.advancedBattlePassPrice"
                    class="card advanced">
                    <div
                        class="btn-buy"
                        :class="{ bought: battlePassStore.hasBuyAdvancedBattlePass }"
                        @click="() => buyBattlePass(BATTLE_PASS_TYPE.ADVANCED, RISK_TYPE.ADVANCED_BATTLE_PASS)">
                        <span v-if="!battlePassStore.hasBuyAdvancedBattlePass">
                            {{ battlePassStore.advancedBattlePassPrice }}豆豆购买
                        </span>
                        <span v-else>已进阶</span>
                    </div>
                </div>
                <div
                    v-if="battlePassStore.superBattlePassPrice"
                    class="card super">
                    <div
                        class="btn-buy"
                        :class="{ bought: battlePassStore.hasBuySuperBattlePass }"
                        @click="() => buyBattlePass(BATTLE_PASS_TYPE.SUPER, RISK_TYPE.SUPER_BATTLE_PASS)">
                        <span v-if="!battlePassStore.hasBuySuperBattlePass">
                            {{ battlePassStore.superBattlePassPrice }}豆豆{{ battlePassStore.hasBuyAdvancedBattlePass ? '升级' : '购买' }}
                        </span>
                        <span v-else>已进阶</span>
                        <div class="tag"></div>
                    </div>
                </div>
            </div>
        </div>
    </popup-container>
</template>

<script setup>
import { upgradeBattlePass } from '@/api';
import { BATTLE_PASS_EXP_REWARD, handleRewardId } from '@/utils/reward';
import useTasksStore from '@/stores/modules/use-tasks-store';
import useBattlePassStore, { BATTLE_PASS_TYPE } from '@/stores/modules/use-battle-pass-store';
import useRiskStore, { RISK_TYPE } from '@/stores/modules/use-risk-store';
import useBoxStore from '@/stores/modules/use-box-store';
import useInitStore from '@/stores/modules/use-init-store';
import useLevelStore from '@/stores/modules/use-level-store';
import useTabStore, { TABS } from '@/stores/modules/use-tab-store';

const battlePassStore = useBattlePassStore();
const boxStore = useBoxStore();
const tasksStore = useTasksStore();
const tabStore = useTabStore();
const initStore = useInitStore();
const levelStore = useLevelStore();
const riskStore = useRiskStore();
const isShow = ref(false);
const loading = ref({ buy: false });
const someLoading = computed(
    () =>
        isShow.value
        && (loading.value.buy || Object.values(riskStore.loading).some(status => !!status)),
);

const BATTLE_PASS_REWARDS = [
    { ...handleRewardId('BP_Gift1') },
    { ...handleRewardId('BP_Gift1') },
    { ...handleRewardId('BP_Gift1') },
    { ...handleRewardId('BP_Gift1') },
    { ...handleRewardId('BP_Gift1') },
    { ...handleRewardId('BP_Gift1') },
    { ...handleRewardId('BP_Gift1'), super: true },
];

const RANK_REWARDS = [
    { ...handleRewardId('BP_Gift1') },
    { ...handleRewardId('BP_Gift1') },
];

useEventBus('modal-buy-battle-pass').on(async ({ show = true }) => {
    isShow.value = show;
    if (isShow.value) {
        await initStore.init();
    }
});

async function buyBattlePass(battlePassType, riskType) {
    if (someLoading.value) {
        return;
    }
    // 避免双端操作
    await initStore.init();
    if (
        (battlePassType === BATTLE_PASS_TYPE.ADVANCED && battlePassStore.hasBuyAdvancedBattlePass)
        || (battlePassType === BATTLE_PASS_TYPE.SUPER && battlePassStore.hasBuySuperBattlePass)
    ) {
        return;
    }
    // 检测是否够T豆
    const price = battlePassType === BATTLE_PASS_TYPE.ADVANCED ? battlePassStore.advancedBattlePassPrice : battlePassStore.superBattlePassPrice;
    if (price <= 0) {
        return;
    }
    const enoughBean = await riskStore.checkBeanEnough(price);
    if (!enoughBean) {
        return;
    }
    // 风控
    const riskSuccess = await riskStore.checkRiskSuccess(riskType);
    if (!riskSuccess) {
        return;
    }
    // 买通行证
    loading.value.buy = true;
    const [res] = await upgradeBattlePass({ type: battlePassType });
    if (res?.code === 0) {
        await initStore.init();
        await levelStore.queryLevelsDetails();
        if (res?.code === 0) {
            if (tabStore.currentTabId === TABS.TAB2) {
                await tasksStore.queryTasks();
            }
            const rewards = (res?.data?.rewards || [])
                .reduce((_, item) => {
                    const details = handleRewardId(item.id);
                    if (details) {
                        _.push({ ...item, ...details });
                    }
                    return _;
                }, []);
            if (res?.data?.getExtraExp) {
                rewards.push({ ...BATTLE_PASS_EXP_REWARD, mark: res.data.getExtraExp });
            }
            if (rewards.length) {
                useEventBus('modal-buy-battle-pass-result').emit({
                    show: true,
                    battlePassType,
                    rewards,
                });
            }
            else if (!boxStore.hasOpenBox) {
                showToast('已成功进阶，宝箱经验值已解锁，记得去领取哦~');
            }
        }
    }
    loading.value.buy = false;
}

function close() {
    isShow.value = false;
}
</script>

<style lang="less" scoped>
.modal-buy-battle-pass {
    .pic-bg(url('images/<EMAIL>'), 375px, 630px);
    padding-top: 320px;
    .icon-close {
        .pic-bg(url('images/<EMAIL>'), 21px, 21px);
        position: absolute;
        top: 11.5px;
        right: 11.5px;
    }
    .battle-pass-rewards {
        .reward {
            .flex-center();
            position: absolute;
            background: url('images/<EMAIL>') no-repeat;
            background-size: 100% 100%;
            img {
                max-width: 70%;
                max-height: 70%;
            }
            &.super {
                background: url('images/<EMAIL>') no-repeat;
                background-size: 100% 100%;
                .reward-super-tag {
                    .pic-bg(url('images/<EMAIL>'), 41px, 25.5px);
                    position: absolute;
                    right: 0;
                    top: 6px;
                }
            }
            &.reward-1 {
                left: 22.5px;
                top: 90.5px;
                width: 96px;
                height: 96px;
            }
            &.reward-2 {
                left: 164.5px;
                top: 97px;
                width: 96px;
                height: 96px;
            }
            &.reward-3 {
                left: 272px;
                top: 95px;
                width: 79.5px;
                height: 79.5px;
            }
            &.reward-4 {
                left: 12.5px;
                top: 200px;
                width: 88.5px;
                height: 88.5px;
            }
            &.reward-5 {
                left: 96.5px;
                top: 170px;
                width: 78px;
                height: 78px;
            }
            &.reward-6 {
                left: 181px;
                top: 207px;
                width: 63.5px;
                height: 63.5px;
            }
            &.reward-7 {
                left: 250.5px;
                top: 186.5px;
                width: 111px;
                height: 111px;
            }
        }
    }
    .rank-rewards {
        .pic-bg(url('images/<EMAIL>'), 350px, 67px);
        display: flex;
        align-items: center;
        padding-left: 11.5px;
        margin: 0 auto;
        .reward {
            .pic-bg(url('images/<EMAIL>'), 44px, 44px);
            .flex-center();
            margin-right: 4.5px;
            img {
                max-width: 80%;
                max-height: 80%;
            }
        }
        .text {
            margin-left: 2px;
            font-size: 12px;
            font-family:
                Alibaba PuHuiTi 2,
                Alibaba PuHuiTi 2-55 Regular;
            font-weight: normal;
            text-align: left;
            color: #ffffff;
            line-height: 15px;
            letter-spacing: -0.24px;
        }
    }
    .battle-pass {
        .flex-space-between();
        padding: 0 9.5px;
        .card {
            position: relative;
            margin-top: 18.5px;
            &.advanced {
                .pic-bg(url('images/<EMAIL>'), 175px, 209.5px);
            }
            &.super {
                .pic-bg(url('images/<EMAIL>'), 175px, 209.5px);
            }
            .btn-buy {
                .x-center();
                .flex-center();
                bottom: 11px;
                width: 155px;
                height: 50px;
                background: #f1cc29;
                border: 2px dashed #f1e194;
                border-radius: 22px;
                &.bought {
                    background: #c5c5c6;
                    border: 2px solid #dddddd;
                    > span {
                        .text-fill-around(#fff, #8d8d8d, 1px);
                    }
                }
                > span {
                    .text-fill-around(#fff, #d28500, 1px);
                    font-size: 18px;
                    font-family:
                        Douyin Sans,
                        Douyin Sans-Bold;
                    font-weight: bold;
                    font-style: italic;
                    text-align: center;
                    color: #ffffff;
                    line-height: 22.5px;
                    letter-spacing: -0.36px;
                }
                .tag {
                    .pic-bg(url('images/<EMAIL>'), 35px, 18.5px);
                    position: absolute;
                    right: 3px;
                    top: -6.5px;
                }
            }
        }
    }
}
</style>
