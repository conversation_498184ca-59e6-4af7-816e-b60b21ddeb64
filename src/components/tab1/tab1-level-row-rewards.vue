<template>
    <div
        :class="{ center: rewards?.length === 1 }"
        class="tab1-level-row-rewards"
    >
        <tab1-level-single-reward
            v-for="(reward, index) in rewards"
            :key="`${reward.battlePassType}-${reward.level}-${reward.id}-${reward.status}`"
            :class="{ last: isLast(index) }"
            :reward="reward" />
    </div>
</template>

<script setup>
const props = defineProps({
    rewards: {
        type: Array,
        required: true,
    },
});

function isLast(index) {
    const row = Math.ceil(props.rewards.length / 2);
    const lastIndexs = [row * 2 - 1, row * 2 - 2];
    return lastIndexs.includes(index);
}
</script>

<style lang="less" scoped>
.tab1-level-row-rewards {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    padding: 0 9px;
    &.center {
        justify-content: center;
    }
    .last {
        margin-bottom: 0 !important;
    }
}
</style>
