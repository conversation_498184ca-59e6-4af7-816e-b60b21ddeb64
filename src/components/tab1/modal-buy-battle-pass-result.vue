<template>
    <common-modal-container
        v-model:show="isShow"
        :bg="bg"
        :title="title"
    >
        <div
            v-if="isShow"
            class="modal-buy-battle-pass-result">
            <div class="top-tip">{{ tip }}</div>
            <div class="rewards">
                <common-modal-reward
                    v-for="(reward, index) in rewards"
                    :key="`${reward.resource_id}-${index}`"
                    :reward="reward"
                    class="reward" />
            </div>
            <div class="result-tip">升级通行证还可领取更多奖励!</div>
            <div
                class="btn"
                @click="linkToUpgrade">
                去升级
            </div>
        </div>
    </common-modal-container>
</template>

<script setup>
import { cloneDeep } from 'lodash-es';
import { BATTLE_PASS_TYPE } from '@/stores/modules/use-battle-pass-store';
import useTabStore, { TABS } from '@/stores/modules/use-tab-store';

const tabStore = useTabStore();
const bg = ref({ width: 375, height: 355.5, url: requireImg('<EMAIL>') });
const title = ref('');
const tip = ref('');
const rewards = ref([]);
const isShow = ref(false);

useEventBus('modal-buy-battle-pass-result').on((options) => {
    isShow.value = options.show;
    if (options.show) {
        title.value = options.battlePassType === BATTLE_PASS_TYPE.ADVANCED ? '已成功完成进阶' : '已成功完成豪华进阶';
        tip.value = options.battlePassType === BATTLE_PASS_TYPE.ADVANCED ? '领取进阶经验值,升级更快啦!' : '领取限定奖励';
        rewards.value = cloneDeep(options.rewards);
    }
});

watch(() => isShow.value, (val) => {
    if (!val) {
        setTimeout(() => {
            title.value = '';
            rewards.value = [];
        }, 300);
    }
});

function linkToUpgrade() {
    tabStore.setTabId(TABS.TAB1);
    isShow.value = false;
    useEventBus('modal-buy-battle-pass').emit({ show: false });
}
</script>

<style lang="less" scoped>
.modal-buy-battle-pass-result {
    position: relative;
    .top-tip {
        margin: 15px auto 0;
        font-size: 12px;
        font-family:
            Alibaba PuHuiTi 2,
            Alibaba PuHuiTi 2-55 Regular;
        font-weight: normal;
        text-align: center;
        color: #62fff6;
        line-height: 15px;
        letter-spacing: -0.24px;
    }
    .rewards {
        margin: 15px auto 0;
        width: 100%;
        display: flex;
        justify-content: center;
        .reward {
            margin-right: 14px;
            &:last-of-type {
                margin-right: 0;
            }
        }
    }
    .result-tip {
        margin: 10px auto 0;
        font-size: 14px;
        font-family:
            Alibaba PuHuiTi 2,
            Alibaba PuHuiTi 2-75 SemiBold;
        font-weight: normal;
        text-align: center;
        color: #ffffff;
        line-height: 15px;
        letter-spacing: -0.28px;
    }
    .btn {
        .pic-bg(url('images/<EMAIL>'), 153px, 48px);
        .flex-center();
        margin: 10px auto 0;
    }
}
</style>
