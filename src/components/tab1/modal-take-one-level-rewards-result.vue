<template>
    <common-modal-container
        v-model:show="isShow"
        :bg="bg"
        :title="title"
    >
        <div
            v-if="isShow"
            class="modal-take-one-level-rewards-result">
            <div
                class="rewards"
                :class="{ center: isCenter }">
                <div class="rewards-list gain-rewards-list">
                    <common-modal-reward
                        v-for="(reward, index) in gainRewards"
                        :key="`${reward.resource_id}-${index}`"
                        :reward="reward"
                        class="reward"
                        size="small"
                    />
                </div>
                <div
                    v-if="showForwardRewards"
                    class="forward-rewards-container">
                    <div class="rewards-list forward-reward-list">
                        <common-modal-reward
                            v-for="(reward, index) in forwardRewards"
                            :key="`${reward.resource_id}-${index}`"
                            :reward="reward"
                            :show-tag="true"
                            color="light-blue"
                            class="reward"
                            size="small"
                        />
                    </div>
                </div>
            </div>
        </div>
    </common-modal-container>
</template>

<script setup>
import { cloneDeep } from 'lodash-es';
import useBattlePassStore from '@/stores/modules/use-battle-pass-store';

const battlePassStore = useBattlePassStore();
const bg = ref({ width: 375, height: 480, url: requireImg('<EMAIL>') });
const title = ref('');
const gainRewards = ref([]);
const forwardRewards = ref([]);
const isShow = ref(false);
const isCenter = computed(() => {
    if (!forwardRewards.value.length) {
        return Math.ceil(gainRewards.value.length / 4) <= 4;
    }
    return Math.ceil(gainRewards.value.length / 4) + Math.ceil(forwardRewards.value.length / 4) <= 3;
});
const showForwardRewards = computed(() => !battlePassStore.hasBuyAllBattlePass && forwardRewards.value.length);

useEventBus('modal-take-one-level-rewards-result').on((options) => {
    isShow.value = !!options.show;
    if (isShow.value) {
        title.value = `升级到LV${options.level}获得奖励`;
        gainRewards.value = cloneDeep(options.gainRewards);
        forwardRewards.value = cloneDeep(options.forwardRewards);
    }
});

watch(() => isShow.value, (val) => {
    if (!val) {
        setTimeout(() => {
            title.value = '';
            gainRewards.value = [];
            forwardRewards.value = [];
        }, 300);
    }
});
</script>

<style lang="less" scoped>
.modal-take-one-level-rewards-result {
    position: relative;
    padding: 10px 0 0 0;
    margin: 0 auto;
    width: 310px;
    height: 99%;
    .rewards {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;
        min-height: 100%;
        &.center {
            justify-content: center;
        }
        .rewards-list {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            width: 100%;
            .reward {
                margin-right: 13.5px;
                &:nth-of-type(4n),
                &:last-of-type {
                    margin-right: 0;
                }
            }
        }
        .forward-rewards-container {
            .point-9-vert(url('images/<EMAIL>'), 35, 8);
            margin-top: 12px;
            padding-top: 44px;
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
        }
    }
}
</style>
