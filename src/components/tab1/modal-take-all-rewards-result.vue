<template>
    <common-modal-container
        v-model:show="isShow"
        :bg="bg"
        :title="title"
    >
        <div
            v-if="isShow"
            class="modal-take-all-rewards-result">
            <div
                class="rewards"
                :class="[`status-${status}`, { center: isCenter }]">
                <div class="rewards-list gain-rewards-list">
                    <common-modal-reward
                        v-for="(reward, index) in gainRewards"
                        :key="`${reward.resource_id}-${index}`"
                        :reward="reward"
                        class="reward"
                        size="small"
                    />
                </div>
                <div
                    v-if="forwardRewards.length && showForwardRewards"
                    class="forward-rewards-container">
                    <div class="rewards-list forward-reward-list">
                        <common-modal-reward
                            v-for="(reward, index) in forwardRewards"
                            :key="`${reward.resource_id}-${index}`"
                            :reward="reward"
                            :show-tag="true"
                            color="light-pink"
                            class="reward"
                            size="small"
                        />
                    </div>
                </div>
            </div>
            <div class="btns">
                <div
                    v-if="hasReciveButton"
                    class="btn btn-recive"
                    @click="close">
                    <span>收下奖励</span>
                </div>
                <div
                    v-if="hasBuyBattlePassButton"
                    class="btn btn-to-buy-battle-pass"
                    @click="toBuyBattlePass">
                    <span>去进阶</span>
                    <div
                        v-if="status === STATUS.IS_MAX_LEVEL_NOT_BUY_ALL_BATTLE_PASS"
                        class="tip long">
                        豪华进阶还可开启榜单争夺哦!
                    </div>
                </div>
                <div
                    v-if="hasSeeRankButton"
                    class="btn btn-see-rank"
                    @click="seeRank">
                    <span>去开启</span>
                    <div
                        v-if="status === STATUS.IS_MAX_LEVEL_BUY_ALL_BATTLE_PASS"
                        class="tip short">
                        立即开启榜单争夺吧!
                    </div>
                </div>
            </div>
        </div>
    </common-modal-container>
</template>

<script setup>
import useBattlePassStore from '@/stores/modules/use-battle-pass-store';
import useLevelStore from '@/stores/modules/use-level-store';
import useTabStore, { TABS } from '@/stores/modules/use-tab-store';

const STATUS = {
    NOT_MAX_LEVEL_NOT_BUY_ALL_BATTLE_PASS: 1,
    NOT_MAX_LEVEL_BUY_ALL_BATTLE_PASS: 2,
    IS_MAX_LEVEL_NOT_BUY_ALL_BATTLE_PASS: 3,
    IS_MAX_LEVEL_BUY_ALL_BATTLE_PASS: 4,
};

const STATUS_BG = {
    [STATUS.NOT_MAX_LEVEL_NOT_BUY_ALL_BATTLE_PASS]: { width: 375, height: 480, url: requireImg('<EMAIL>') },
    [STATUS.NOT_MAX_LEVEL_BUY_ALL_BATTLE_PASS]: { width: 375, height: 345, url: requireImg('<EMAIL>') },
    [STATUS.IS_MAX_LEVEL_NOT_BUY_ALL_BATTLE_PASS]: { width: 375, height: 505, url: requireImg('<EMAIL>') },
    [STATUS.IS_MAX_LEVEL_BUY_ALL_BATTLE_PASS]: { width: 375, height: 385, url: requireImg('<EMAIL>') },
};

const tabStore = useTabStore();
const battlePassStore = useBattlePassStore();
const levelStore = useLevelStore();
const isShow = ref(false);
const gainRewards = ref([]);
const forwardRewards = ref([]);

const status = computed(() => {
    if (levelStore.isReachMaxLevel && !battlePassStore.hasBuyAllBattlePass) {
        return STATUS.IS_MAX_LEVEL_NOT_BUY_ALL_BATTLE_PASS;
    }
    else if (levelStore.isReachMaxLevel && battlePassStore.hasBuyAllBattlePass) {
        return STATUS.IS_MAX_LEVEL_BUY_ALL_BATTLE_PASS;
    }
    else if (!levelStore.isReachMaxLevel && !battlePassStore.hasBuyAllBattlePass) {
        return STATUS.NOT_MAX_LEVEL_NOT_BUY_ALL_BATTLE_PASS;
    }
    else {
        return STATUS.NOT_MAX_LEVEL_BUY_ALL_BATTLE_PASS;
    }
});
const isCenter = computed(() => {
    if (status.value === STATUS.NOT_MAX_LEVEL_NOT_BUY_ALL_BATTLE_PASS) {
        return Math.ceil(gainRewards.value.length / 4) + Math.ceil(forwardRewards.value.length / 4) <= 3;
    }
    else if (status.value === STATUS.NOT_MAX_LEVEL_BUY_ALL_BATTLE_PASS) {
        return Math.ceil(gainRewards.value.length / 4) <= 2;
    }
    else if (status.value === STATUS.IS_MAX_LEVEL_NOT_BUY_ALL_BATTLE_PASS) {
        return Math.ceil(gainRewards.value.length / 4) + Math.ceil(forwardRewards.value.length / 4) <= 3;
    }
    else {
        return Math.ceil(gainRewards.value.length / 4) <= 2;
    }
});
const bg = computed(() => STATUS_BG[status.value]);
const title = computed(() => levelStore.isReachMaxLevel ? '恭喜！通行证已满级' : '成功领取');
const showForwardRewards = computed(() => !battlePassStore.hasBuyAllBattlePass && forwardRewards.value.length);
const hasReciveButton = computed(() => [STATUS.NOT_MAX_LEVEL_NOT_BUY_ALL_BATTLE_PASS, STATUS.NOT_MAX_LEVEL_BUY_ALL_BATTLE_PASS, STATUS.IS_MAX_LEVEL_NOT_BUY_ALL_BATTLE_PASS].includes(status.value));
const hasBuyBattlePassButton = computed(() => [STATUS.NOT_MAX_LEVEL_NOT_BUY_ALL_BATTLE_PASS, STATUS.IS_MAX_LEVEL_NOT_BUY_ALL_BATTLE_PASS].includes(status.value));
const hasSeeRankButton = computed(() => [STATUS.IS_MAX_LEVEL_BUY_ALL_BATTLE_PASS].includes(status.value));

useEventBus('modal-take-all-rewards-result').on((options) => {
    isShow.value = !!options.show;
    if (isShow.value) {
        gainRewards.value = options.gainRewards;
        forwardRewards.value = options.forwardRewards;
    }
});

watch(() => isShow.value, (val) => {
    if (!val) {
        setTimeout(() => {
            gainRewards.value = [];
            forwardRewards.value = [];
        }, 300);
    }
});

function toBuyBattlePass() {
    close();
    useEventBus('modal-buy-battle-pass').emit({ show: true });
}

function seeRank() {
    close();
    tabStore.setTabId(TABS.TAB4);
}

function close() {
    isShow.value = false;
}
</script>

<style lang="less" scoped>
.modal-take-all-rewards-result {
    display: flex;
    flex-direction: column;
    position: relative;
    margin: 0 auto;
    width: 310px;
    height: 99%;
    .rewards {
        position: absolute;
        left: 0;
        top: 10px;
        width: 100%;
        padding: 2px 0 20px 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        overflow: scroll;
        &.status-1 {
            height: 355px;
        }
        &.status-2 {
            height: 225px;
        }
        &.status-3 {
            height: 382px;
        }
        &.status-4 {
            height: 250px;
        }
        &.center {
            justify-content: center;
        }
        .rewards-list {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            width: 100%;
            .reward {
                margin-right: 13.5px;
                &:nth-of-type(4n),
                &:last-of-type {
                    margin-right: 0;
                }
            }
        }
        .forward-rewards-container {
            .point-9-vert(url('images/<EMAIL>'), 29.5, 69.5);
            padding-top: 44px;
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
        }
    }
    .btns {
        display: flex;
        justify-content: center;
        position: absolute;
        bottom: 7px;
        left: 0;
        width: 100%;
        height: 50px;
        .btn {
            .flex-center();
            position: relative;
            margin-right: 19px;
            width: 125px;
            height: 50px;
            &:last-of-type {
                margin-right: 0 !important;
            }
            &.btn-recive {
                width: 125px;
                height: 50px;
                background: linear-gradient(0deg, #209cff 0%, #68e0cf 100%), #2d94db;
                border: 2px solid #94f1e9;
                border-radius: 22px;
            }
            &.btn-to-buy-battle-pass {
                width: 125px;
                height: 50px;
                background: #f1cc29;
                border: 2px solid #f1e194;
                border-radius: 22px;
            }
            &.btn-see-rank {
                width: 125px;
                height: 50px;
                background: #f1cc29;
                border: 2px solid #f1e194;
                border-radius: 22px;
            }
            .tip {
                .flex-center();
                .x-center();
                top: -33px;
                font-size: 14px;
                font-family:
                    Source Han Sans CN,
                    Source Han Sans CN-Medium;
                font-weight: 500;
                text-align: center;
                color: #f74c30;
                line-height: 15px;
                letter-spacing: -0.28px;
                &.long {
                    .pic-bg(url('images/<EMAIL>'), 205px, 29px);
                }
                &.short {
                    .pic-bg(url('images/<EMAIL>'), 155px, 29px);
                }
            }
        }
    }
}
</style>
