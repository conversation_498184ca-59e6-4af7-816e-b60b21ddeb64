<template>
    <div
        :class="[`type-${reward.battlePassType}`]"
        class="reward"
        @click="handleRewardClick">
        <span class="absolute left-[0] top-[-11px] text-[10px] font-800">{{ reward.resource_id }}</span>
        <div
            v-if="canTake"
            class="reward-active-wrapper"></div>
        <div class="reward-image">
            <img :src="reward?.url">
        </div>
        <div
            v-if="!canTake || !enoughLevel"
            class="reward-status"
            :class="{
                locked: !reward?.status,
                unlocked: reward?.status,
            }"></div>
        <div
            v-if="count"
            class="reward-count">
            {{ count }}
        </div>
    </div>
</template>

<script setup>
import { cloneDeep } from 'lodash-es';
import { receiveReward } from '@/api';
import useLoading from '@/hooks/use-loading';
import useInitStore from '@/stores/modules/use-init-store';
import useLevelStore from '@/stores/modules/use-level-store';
import useBattlePassStore, { BATTLE_PASS_TYPE } from '@/stores/modules/use-battle-pass-store';
import { handleRewardId } from '@/utils/reward';

const props = defineProps({
    reward: {
        type: Object,
        required: true,
    },
});

const battlePassStore = useBattlePassStore();
const initStore = useInitStore();
const levelStore = useLevelStore();
const loading = ref({ take: false });
const enoughLevel = computed(() => levelStore.currentLevel >= props.reward.level);
const canTake = computed(() => {
    if (props.reward.status) {
        return false;
    }
    if (props.reward.battlePassType === BATTLE_PASS_TYPE.NORMAL) {
        return enoughLevel.value;
    }
    if (props.reward.battlePassType === BATTLE_PASS_TYPE.ADVANCED) {
        return enoughLevel.value && battlePassStore.hasBuyAdvancedBattlePass;
    }
    if (props.reward.battlePassType === BATTLE_PASS_TYPE.SUPER) {
        return enoughLevel.value && battlePassStore.hasBuySuperBattlePass;
    }
    return false;
});
const count = computed(() => props.reward.num || props.reward.time || 0);

useLoading(computed(() => loading.value.take));

async function handleRewardClick() {
    if (loading.value.take) {
        return;
    }
    if (canTake.value) {
        loading.value.take = true;
        const [res] = await receiveReward({ level: props.reward.level });
        const gainRewards = (res?.data?.rewards || [])
            .reduce((_, item) => {
                const details = handleRewardId(item.id);
                if (details) {
                    _.push({ ...item, ...details, level: props.reward.level });
                }
                return _;
            }, []);
        if (gainRewards.length) {
            await initStore.init();
            await levelStore.queryLevelsDetails();
            const forwardRewards = [...levelStore.unTakenRewards.super, ...levelStore.unTakenRewards.advanced]
                .filter(reward => reward.level === props.reward.level);
            useEventBus('modal-take-one-level-rewards-result').emit({
                show: true,
                gainRewards,
                forwardRewards,
                level: props.reward.level,
            });
        }
        loading.value.take = false;
    }
    else {
        useEventBus('modal-level-reward-details').emit({
            show: true,
            reward: cloneDeep(props.reward),
        });
    }
}
</script>

<style lang="less" scoped>
.reward {
    position: relative;
    margin-bottom: 11px;
    width: 44px;
    height: 44px;
    border-radius: 5px;
    &.active {
        border: 2.5px solid #fdff6e !important;
    }
    &.type-1 {
        background: #768ec7;
        border: 2px solid #c6d2f0;
    }
    &.type-2 {
        background: linear-gradient(0deg, #4ca3e2 0%, #2a5fa7 100%), #bbe9f0;
        border: 2px solid #85cdea;
    }
    &.type-3 {
        background: linear-gradient(0deg, #ff2ef3 0%, #b81bcc 100%), #bbe9f0;
        border: 2px solid #fca7fb;
    }
    .reward-active-wrapper {
        .x-y-center();
        .pic-bg(url('images/<EMAIL>'), 51px, 51px);
    }
    .reward-image {
        .flex-center();
        width: 100%;
        height: 100%;
        img {
            max-width: 80%;
            max-height: 80%;
        }
    }
    .reward-status {
        position: absolute;
        right: -2.5px;
        top: -5.5px;
        &.locked {
            .pic-bg(url('images/<EMAIL>'), 18px, 18px);
        }
        &.unlocked {
            .pic-bg(url('images/<EMAIL>'), 18px, 18px);
        }
    }
    .reward-count {
        .text-fill-around(#fff, #AD5A00);
        position: absolute;
        right: 3px;
        bottom: 0;
        font-size: 12px;
        font-family:
            Douyin Sans,
            Douyin Sans-Bold;
        font-weight: 700;
        text-align: center;
        color: #ffffff;
        line-height: 15px;
        letter-spacing: -0.24px;
    }
}
</style>
