<template>
    <div class="tab1-next-big-level">
        <div class="content">
            <tab1-level-num
                class="w-[42px]"
                :level="levelStore.closestBigLevel"
                :locked="true" />
            <tab1-level-row-rewards
                v-for="col in BATTLE_PASS_TYPE_COLS"
                :key="col.key"
                class="w-[111px]"
                :rewards="levelStore.closestBigLevelRewards[col.key]"
            />
        </div>
    </div>
</template>

<script setup>
import { BATTLE_PASS_TYPE_COLS } from '@/stores/modules/use-battle-pass-store';
import useLevelStore from '@/stores/modules/use-level-store';

const levelStore = useLevelStore();
</script>

<style lang="less" scoped>
.tab1-next-big-level {
    .point-9-vert(url('images/<EMAIL>'), 15, 15);
    padding: 15px 0 15px 0;
    display: flex;
    align-items: center;
    overflow: scroll;
    .content {
        display: flex;
    }
}
</style>
