<template>
    <common-modal-container
        v-model:show="isShow"
        :bg="bg"
        :title="title"
    >
        <div
            v-if="isShow"
            class="modal-buy-level">
            <div class="tip">升级到{{ levelStore.currentLevel + currentSelectUpgradeLevelCount }}级可领取以下奖励</div>
            <div
                class="rewards"
                :class="{ center: upgradeRewards.length <= 8 }">
                <common-modal-reward
                    v-for="(reward, index) in upgradeRewards"
                    :key="`${reward.resource_id}-${index}`"
                    :show-total-price="true"
                    :reward="reward"
                    size="small"
                    class="reward">
                </common-modal-reward>
            </div>
            <div class="count-selector">
                <div
                    v-if="currentSelectUpgradeLevelCount > minSelectUpgradeLevelCount"
                    class="selector left-[0]"
                    @click="decrease">
                    -
                </div>
                <div
                    v-if="currentSelectUpgradeLevelCount < maxSelectUpgradeLevelCount"
                    class="selector right-[0]"
                    @click="increase">
                    +
                </div>
                <div class="num">{{ currentSelectUpgradeLevelCount }}</div>
            </div>
            <div
                class="btn-buy"
                @click="buy">
                {{ currentSelectUpgradeLevelBeanCount }}豆购买
            </div>
        </div>
    </common-modal-container>
</template>

<script setup>
import { cloneDeep } from 'lodash-es';
import { buyLevel } from '@/api';
import useRiskStore, { RISK_TYPE } from '@/stores/modules/use-risk-store';
import useLevelStore from '@/stores/modules/use-level-store';
import useBattlePassStore from '@/stores/modules/use-battle-pass-store';
import useInitStore from '@/stores/modules/use-init-store';
import useLoading from '@/hooks/use-loading';

const initStore = useInitStore();
const riskStore = useRiskStore();
const levelStore = useLevelStore();
const battlePassStore = useBattlePassStore();

const isShow = ref(false);
const loading = ref({ buy: false });
const cantBuy = computed(() => isShow.value && (loading.value.buy || Object.values(riskStore.loading).some(status => !!status)));

useLoading(computed(() => isShow.value && (loading.value.buy || riskStore.loading.bean))); // 不放riskStore.loading.risk，因为会挡住人脸弹窗无法点击

const currentSelectUpgradeLevelCount = ref(0); // 当前选择升级级数数量
const minSelectUpgradeLevelCount = ref(0); // 最小可升级级数数量
const maxSelectUpgradeLevelCount = ref(0); // 最大可升级级数数量
const currentSelectUpgradeLevelBeanCount = computed(() => currentSelectUpgradeLevelCount.value * levelStore.everyLevelPrize); // 当前选择升级级数所需T豆数量

const upgradeRewards = ref([]); // 升级后可以解锁的奖励
const bg = ref({ width: 375, height: 415.5, url: requireImg('<EMAIL>') });
const title = computed(() => `购买${currentSelectUpgradeLevelCount.value}等级`);

useEventBus('modal-buy-level').on(({ show = true }) => {
    isShow.value = show;
    if (isShow.value) {
        currentSelectUpgradeLevelCount.value = levelStore.canBuyLevelCount ? 1 : 0;
        minSelectUpgradeLevelCount.value = levelStore.canBuyLevelCount ? 1 : 0;
        maxSelectUpgradeLevelCount.value = levelStore.canBuyLevelCount ? levelStore.canBuyLevelCount : 0;
        refreshUpgradeRewards();
    }
});

watch(() => isShow.value, (val) => {
    if (!val) {
        setTimeout(() => {
            currentSelectUpgradeLevelCount.value = 0;
            minSelectUpgradeLevelCount.value = 0;
            maxSelectUpgradeLevelCount.value = 0;
            refreshUpgradeRewards();
        }, 300);
    }
});

function refreshUpgradeRewards() {
    const rewards = cloneDeep(
        levelStore.levelsRewards
            .filter(row => row.level > levelStore.currentLevel && row.level <= levelStore.currentLevel + currentSelectUpgradeLevelCount.value)
            .sort((a, b) => a.level - b.level)
            .reduce((_, row) => {
                Array.prototype.push.apply(_, row.normal || []);
                if (battlePassStore.hasBuyAdvancedBattlePass) {
                    Array.prototype.push.apply(_, row.advanced || []);
                }
                if (battlePassStore.hasBuySuperBattlePass) {
                    Array.prototype.push.apply(_, row.super || []);
                }
                return _;
            }, []),
    );
    const deduplicateRewardsMap = rewards.reduce((_, reward) => {
        if (!_[reward.resource_id]) {
            _[reward.resource_id] = reward;
        }
        else {
            _[reward.resource_id].time += reward.time || 0;
            _[reward.resource_id].num += reward.num || 0;
        }
        return _;
    }, {});
    upgradeRewards.value = Object
        .values(deduplicateRewardsMap)
        .sort((a, b) => b.originPrice * b.num - a.originPrice * a.num);
}

function decrease() {
    if (currentSelectUpgradeLevelCount.value > minSelectUpgradeLevelCount.value) {
        currentSelectUpgradeLevelCount.value--;
        refreshUpgradeRewards();
    }
}

function increase() {
    if (currentSelectUpgradeLevelCount.value < maxSelectUpgradeLevelCount.value) {
        currentSelectUpgradeLevelCount.value++;
        refreshUpgradeRewards();
    }
}

async function buy() {
    if (cantBuy.value) {
        return;
    }
    if (!currentSelectUpgradeLevelCount.value) {
        showToast('请选择升级等级');
        return;
    }
    // 检测是否够T豆
    const price = levelStore.everyLevelPrize * currentSelectUpgradeLevelCount.value;
    if (price <= 0) {
        return;
    }
    const enoughBean = await riskStore.checkBeanEnough(price);
    if (!enoughBean) {
        return;
    }
    // 风控
    const riskSuccess = await riskStore.checkRiskSuccess(RISK_TYPE.LEVEL);
    if (!riskSuccess) {
        return;
    }
    // 买等级
    loading.value.buy = true;
    const [res] = await buyLevel({ level: currentSelectUpgradeLevelCount.value });
    if (res?.code === 0) {
        await initStore.init();
        await levelStore.queryLevelsDetails();
        showToast('购买成功,快去领取奖励吧');
        close();
    }
    loading.value.buy = false;
}

function close() {
    isShow.value = false;
}
</script>

<style lang="less" scoped>
.modal-buy-level {
    .tip {
        margin: 10px auto 0;
        font-size: 12px;
        font-family:
            Alibaba PuHuiTi 2,
            Alibaba PuHuiTi 2-55 Regular;
        font-weight: normal;
        text-align: center;
        color: #62fff6;
        line-height: 15px;
        letter-spacing: -0.24px;
    }
    .rewards {
        // .tb();
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        margin-top: 12px;
        padding: 0 40px;
        min-height: 203px;
        max-height: 218px;
        overflow-x: hidden;
        overflow-y: scroll;
        &.center {
            align-items: center;
        }
        .reward {
            position: relative;
            width: 60.75px;
            margin-bottom: 40.75px;
            margin-right: 13.45px;
            &:nth-of-type(4n),
            &:last-of-type {
                margin-right: 0;
            }
            .reward-image {
                .pic-bg(url('images/<EMAIL>'), 60.75px, 60.75px);
                .flex-center();
                position: relative;
                img {
                    max-width: 80%;
                    max-height: 80%;
                }
                .reward-count {
                    .text-fill-around(#fff, #ad5a00, 1px);
                    position: absolute;
                    right: 4px;
                    bottom: 3px;
                    font-size: 12px;
                    font-family:
                        Douyin Sans,
                        Douyin Sans-Bold;
                    font-weight: 700;
                    text-align: center;
                    color: #ffffff;
                    line-height: 15px;
                    letter-spacing: -0.24px;
                }
            }
            .reward-text {
                .x-center();
                top: 65px;
                width: 110%;
                font-size: 12px;
                font-family:
                    Alibaba PuHuiTi 2,
                    Alibaba PuHuiTi 2-55 Regular;
                font-weight: normal;
                text-align: center;
                color: #fff99c;
                line-height: 15px;
                letter-spacing: -0.24px;
                > div {
                    .one-line();
                    width: 100%;
                }
            }
        }
    }
    .count-selector {
        .pic-bg(url('images/<EMAIL>'), 128px, 28px);
        .flex-center();
        margin: 10px auto 0;
        position: relative;
        .selector {
            .flex-center();
            position: absolute;
            top: 0;
            height: 28px;
            width: 28px;
            font-size: 14px;
            font-family:
                Alibaba PuHuiTi 2,
                Alibaba PuHuiTi 2-75 SemiBold;
            font-weight: normal;
            text-align: center;
            color: #72b4ff;
            letter-spacing: -0.28px;
        }
        .num {
            font-size: 14px;
            font-family:
                Alibaba PuHuiTi 2,
                Alibaba PuHuiTi 2-75 SemiBold;
            font-weight: normal;
            text-align: center;
            color: #ffffff;
            line-height: 15px;
            letter-spacing: -0.28px;
        }
    }
    .btn-buy {
        .pic-bg(url('images/<EMAIL>'), 193px, 48px);
        .text-fill-around(#fff, #d28500, 1px);
        .flex-center();
        margin: 10px auto 0;
        font-size: 18px;
        font-family:
            Douyin Sans,
            Douyin Sans-Bold;
        font-weight: bold;
        font-style: italic;
        text-align: center;
        color: #ffffff;
        line-height: 22.5px;
        letter-spacing: -0.36px;
    }
}
</style>
