<template>
    <modal-container
        v-model:show="isShow"
        :close-on-click-overlay="true">
        <div
            v-if="isShow"
            class="modal-no-enough-bean">
            <div
                class="icon-close"
                @click="close"></div>
            <div class="tip">所需T豆数量不足,是否充值?</div>
            <div
                class="btn-back"
                @click="close"></div>
            <div
                class="btn-charge"
                @click="linkToCharge"></div>
        </div>
    </modal-container>
</template>

<script setup>
import { toChange } from '@/utils/jsbridge';

const isShow = ref(false);

useEventBus('modal-no-enough-bean').on(({ show = true }) => {
    isShow.value = show;
});

function linkToCharge() {
    close();
    toChange();
}

function close() {
    isShow.value = false;
}
</script>

<style lang="less" scoped>
.modal-no-enough-bean {
    .pic-bg(url('images/<EMAIL>'), 375px, 165.5px);
    position: relative;
    .icon-close {
        .pic-bg(url('images/<EMAIL>'), 32px, 32px);
        position: absolute;
        right: 42px;
        top: -42px;
    }
    .tip {
        .x-text-center();
        top: 45.5px;
        font-size: 18px;
        font-family:
            Alibaba PuHuiTi 2,
            Alibaba PuHuiTi 2-75 SemiBold;
        font-weight: normal;
        text-align: center;
        color: #ffffff;
        line-height: 15px;
        letter-spacing: -0.36px;
    }
    .btn-back {
        .pic-bg(url('images/<EMAIL>'), 121px, 40px);
        position: absolute;
        left: 57px;
        bottom: 30px;
    }
    .btn-charge {
        .pic-bg(url('images/<EMAIL>'), 121px, 40px);
        position: absolute;
        right: 57px;
        bottom: 30px;
    }
}
</style>
