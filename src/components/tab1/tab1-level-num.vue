<template>
    <div class="tab1-level-num">
        <div :class="{ locked, unlocked: !locked }">
            {{ level }}
        </div>
    </div>
</template>

<script setup>
defineProps({
    level: {
        type: Number,
        required: true,
    },
    locked: {
        type: Boolean,
        required: true,
    },
});
</script>

<style lang="less" scoped>
.tab1-level-num {
    .flex-center();
    > div {
        .flex-center();
        font-size: 18px;
        font-family:
            <PERSON><PERSON><PERSON>s,
            Douyin Sans-Bold;
        font-weight: 700;
        text-align: center;
        color: #ffffff;
        line-height: 13.5px;
        letter-spacing: -0.36px;
        &.locked {
            .pic-bg(url('images/<EMAIL>'), 36.5px, 36.5px);
        }
        &.unlocked {
            .pic-bg(url('images/<EMAIL>'), 36.5px, 36.5px);
        }
    }
}
</style>
