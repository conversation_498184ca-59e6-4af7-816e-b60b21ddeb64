<template>
    <div class="tab2-task-progress">
        <div class="title">{{ task.title }}</div>
        <tab2-task-progress-process
            :task-list="task.stages"
            :value="task.process"
            :use-wrapper-el-width="true"
        />
    </div>
</template>

<script setup>
defineProps({
    task: {
        type: Object,
        required: true,
    },
});
</script>

<style lang="less" scoped>
.tab2-task-progress {
    .pic-bg(url('images/<EMAIL>'), 331px, 112px);
    position: relative;
    padding: 50px 13px 0 13px;
    overflow: hidden;
    .title {
        position: absolute;
        left: 43px;
        top: 15px;
        font-size: 15px;
        font-family:
            Alibaba PuHuiTi 2,
            Alibaba PuHuiTi 2-75 SemiBold;
        font-weight: bold;
        text-align: center;
        color: #423698;
        line-height: 15px;
        letter-spacing: -0.3px;
    }
}
</style>
