<template>
    <div class="tab2-tabs">
        <div
            v-for="({ tab, text }) in tasksStore.tabs"
            :key="tab"
            class="tab"
            :class="[`tab-${tab}`, { active: tasksStore.currentTasksTab === tab }]"
            @click.stop="() => setTab(tab)">
            <span>{{ text }}</span>
            <div
                v-if="tasksStore.currentTasksTab === tab"
                class="line"></div>
        </div>
    </div>
</template>

<script setup>
import useTasksStore from '@/stores/modules/use-tasks-store';
import useInitStore from '@/stores/modules/use-init-store';

const tasksStore = useTasksStore();
const initStore = useInitStore();

async function setTab(tab) {
    if (tasksStore.currentTasksTab !== tab) {
        tasksStore.setCurrentTasksTab(tab);
        await Promise.all([
            initStore.init(),
            tasksStore.queryTasks(),
        ]);
    }
}
</script>

<style lang="less" scoped>
.tab2-tabs {
    .flex-center();
    width: 375px;
    height: 37px;
    background: #304aca;
    border-radius: 15px 15px 0px 0px;
    .tab {
        .flex-center();
        position: relative;
        margin-right: 35px;
        width: 70.5px;
        height: 100%;
        font-size: 14px;
        font-family: HYFengShangHei, HYFengShangHei-85J;
        font-weight: normal;
        text-align: center;
        color: #ebebeb;
        line-height: 15px;
        letter-spacing: -0.28px;
        .line {
            .pic-bg(url('images/<EMAIL>'), 70.5px, 6.5px);
            .x-center();
            bottom: 1px;
        }
        &.active {
            font-size: 16px;
            color: #ffe369;
            letter-spacing: -0.32px;
        }
        &:last-of-type {
            margin-right: 0;
        }
    }
}
</style>
