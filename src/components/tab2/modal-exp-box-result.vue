<template>
    <common-modal-container
        v-model:show="isShow"
        :bg="bg"
        :title="title"
    >
        <div
            v-if="isShow"
            class="modal-exp-box-result">
            <div class="container">
                <div
                    class="rewards"
                    :class="{ center: isCenter }">
                    <common-modal-reward
                        v-for="(reward, index) in rewards"
                        :key="`${reward.resource_id}-${index}`"
                        :reward="reward"
                        class="reward"
                    />
                </div>
                <div
                    v-if="rewards.length === 1"
                    class="tip">
                    进阶后还可额外获得{{ boxStore.battlePassExtraExp }}经验值<br />升级快人一步!
                </div>
            </div>
            <div
                v-if="rewards.length === 1"
                class="btn btn-to-buy-battle-pass"
                @click="toBuyBattlePass">
                去进阶
            </div>
            <div
                v-else
                class="btn btn-recive"
                @click="recive">
                全部领取
            </div>
        </div>
    </common-modal-container>
</template>

<script setup>
import { BASE_EXP_REWARD, BATTLE_PASS_EXP_REWARD } from '@/utils/reward';
import useBoxStore from '@/stores/modules/use-box-store';

const boxStore = useBoxStore();
const bg = ref({ width: 375, height: 355.5, url: requireImg('<EMAIL>') });
const title = ref('已领取经验宝箱');
const isShow = ref(false);
const rewards = ref([]);

useEventBus('modal-exp-box-result').on((options) => {
    isShow.value = !!options.show;
    if (isShow.value) {
        if (options.exp) {
            rewards.value.push({ ...BASE_EXP_REWARD, mark: `${options.exp}` });
        }
        if (options.extraExp) {
            rewards.value.push({ ...BATTLE_PASS_EXP_REWARD, mark: `${options.extraExp}` });
        }
    }
});

watch(() => isShow.value, (val) => {
    if (!val) {
        setTimeout(() => {
            rewards.value = [];
        }, 300);
    }
});

function toBuyBattlePass() {
    useEventBus('modal-buy-battle-pass').emit({ show: true });
    close();
}

function recive() {
    showToast('领取成功');
    close();
}

function close() {
    isShow.value = false;
}
</script>

<style lang="less" scoped>
.modal-exp-box-result {
    position: relative;
    padding: 10px 0 0 0;
    margin: 0 auto;
    width: 310px;
    height: 99%;
    .container {
        .flex-center();
        height: 220px;
        flex-direction: column;
        .rewards {
            .flex-center();
            .reward {
                margin-right: 17px;
                &:last-of-type {
                    margin-right: 0;
                }
            }
        }
        .tip {
            margin-top: 10px;
            font-size: 13px;
            font-family:
                Alibaba PuHuiTi 2,
                Alibaba PuHuiTi 2-75 SemiBold;
            font-weight: normal;
            text-align: center;
            color: #ffffff;
            line-height: 19.5px;
            letter-spacing: -0.26px;
        }
    }
    .btn {
        .pic-bg(url('images/<EMAIL>'), 153.5px, 48px);
        .x-center();
        .flex-center();
        bottom: 30px;
    }
}
</style>
