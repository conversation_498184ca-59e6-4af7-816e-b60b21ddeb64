<template>
    <div class="tab2-task-common">
        <div class="title">{{ task.title }}</div>
        <div class="exp">
            <div class="exp-value">{{ task.exp }}经验值{{ task.unit ? `/${task.unit}` : '' }}</div>
        </div>
        <div
            v-if="task.showType === TASKS_SHOW_TYPE.PERCENT"
            class="progress-num">
            {{ omitValue(task.process) }}/{{ omitValue(task.total) }}
        </div>
        <div
            v-if="task.showType === TASKS_SHOW_TYPE.SHORT_PROGRESS_BAR_PERCENT"
            class="progress-bar">
            <div
                class="progress-content"
                :style="{ width: progressWidth }"></div>
            <span>{{ omitValue(task.process) }}/{{ omitValue(task.total) }}</span>
        </div>
    </div>
</template>

<script setup>
import { TASKS_SHOW_TYPE } from '@/stores/modules/use-tasks-store';

const props = defineProps({
    task: {
        type: Object,
        required: true,
    },
});

const progressWidth = computed(() => props.task.status ? '100%' : `${props.task.process / props.task.total * 100}%`);
</script>

<style lang="less" scoped>
.tab2-task-common {
    .pic-bg(url('images/<EMAIL>'), 331px, 77px);
    position: relative;
    .title {
        position: absolute;
        left: 21px;
        top: 14.5px;
        font-size: 15px;
        font-family:
            Alibaba PuHuiTi 2,
            Alibaba PuHuiTi 2-75 SemiBold;
        font-weight: bold;
        text-align: center;
        color: #423698;
        line-height: 15px;
        letter-spacing: -0.3px;
    }
    .exp {
        .pic-bg(url('images/<EMAIL>'), 93.5px, 27px);
        position: absolute;
        left: 14.5px;
        top: 40.5px;
        .exp-value {
            .flex-center();
            position: absolute;
            left: 14px;
            top: 0;
            height: 100%;
            width: 80px;
            font-size: 11px;
            font-family:
                Source Han Sans CN,
                Source Han Sans CN-Medium;
            font-weight: 500;
            font-style: italic;
            text-align: center;
            color: #ffffff;
            line-height: 16px;
            letter-spacing: -0.22px;
        }
    }
    .progress-num {
        position: absolute;
        right: 47.5px;
        bottom: 18px;
        font-size: 13px;
        font-family: HYFengShangHei, HYFengShangHei-85J;
        font-weight: normal;
        font-style: italic;
        text-align: center;
        color: #2e5fd3;
        line-height: 15px;
        letter-spacing: -0.26px;
    }
    .progress-bar {
        position: absolute;
        left: 114px;
        top: 46.5px;
        width: 83px;
        height: 15.5px;
        background: #47518b;
        border-radius: 8px;
        overflow: hidden;
        .progress-content {
            position: absolute;
            left: 0;
            top: 0;
            width: 0;
            height: 100%;
            background: #fff261;
            border-radius: 8px;
        }
        > span {
            .text-fill-around(#fff, #3748a8, 0.8px);
            .x-center();
            z-index: 10;
            font-size: 12px;
            font-family:
                Alibaba PuHuiTi 2,
                Alibaba PuHuiTi 2-75 SemiBold;
            font-weight: normal;
            text-align: left;
            color: #ffffff;
            line-height: 15px;
            letter-spacing: -0.24px;
        }
    }
}
</style>
