<template>
    <div
        ref="$processBox"
        class="tab2-task-progress-process">
        <div
            ref="$process"
            class="process">
            <div class="process-bg"></div>
            <div
                ref="$processBar"
                class="process-bar"></div>
        </div>
        <ul
            ref="$taskList"
            class="task-list">
            <li
                v-for="(item, index) in taskList"
                ref="$taskItem"
                :key="`task-item-${index}`"
                class="task-item">
                <div class="exp">
                    <div class="exp-value">{{ item.exp }}</div>
                </div>
                <div
                    ref="$taskIcon"
                    class="task-icon"
                    :class="[{ active: item.status }]" />
                <div class="value">
                    {{ omitValue(item.value) }}
                </div>
            </li>
        </ul>
    </div>
</template>

<script>
export default {
    props: {
        taskList: {
            type: Array,
            default: () => ([]),
        },
        value: {
            type: Number,
            default: 0,
        },
        scrollToCurrentValue: {
            type: Boolean,
            default: true,
        },
        useWrapperElWidth: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            barWidth: 0,
            currentIndex: 0,
            widthDistance: [],
            exceedIconWidth: 0,
            iconStatus: [],
        };
    },
    watch: {
        value: {
            immediate: true,
            handler() {
                this.$nextTick(() => {
                    this.setOuterProcessWidth();
                    this.setInnerProcessWidth();
                });
            },
        },
    },
    mounted() {
        if (this.scrollToCurrentValue) {
            this.$watch(
                'currentIndex',
                (val) => {
                    if (val > 0) {
                        const { $processBox, $taskItem } = this.$refs;
                        if ($processBox && $taskItem && $taskItem[val]) {
                            $processBox.scrollLeft = $taskItem[val].offsetLeft;
                        }
                    }
                },
                {
                    immediate: true,
                },
            );
        }
    },
    methods: {
        setOuterProcessWidth() {
            const { $process, $processBox, $processBar, $taskList, $taskIcon } = this.$refs;
            if (!$process || !$processBox || !$processBar || !$taskList || !$taskIcon) {
                return;
            }
            const taskListLeft = $taskList.getBoundingClientRect().left;
            const processLeft = $process.offsetLeft;
            const processBarRadius = Math.round($processBar.clientHeight / 2);
            const iconLeftList = [];
            for (let i = 0; i < $taskIcon.length; i++) {
                iconLeftList[i] = $taskIcon[i].getBoundingClientRect().left - taskListLeft + $taskIcon[i].clientWidth / 2;
                if (i === 0) {
                    this.widthDistance[i] = Math.round(iconLeftList[i] - processLeft + processBarRadius);
                }
                else {
                    this.widthDistance[i] = Math.round(iconLeftList[i] - iconLeftList[i - 1]);
                }
            }
            this.exceedIconWidth = Math.round($taskIcon[0].clientWidth - processBarRadius + 1);
            $process.style.width = `${Math.round(iconLeftList[iconLeftList.length - 1] - processLeft)}px`;
            if (this.useWrapperElWidth) {
                $processBox.style.width = `${$processBox.parentNode.clientWidth}px`;
                $processBox.style.overflow = `scroll`;
            }
            else {
                $processBox.style.width = `${$taskList.offsetWidth}px`;
            }
        },
        setInnerProcessWidth() {
            const { $processBar } = this.$refs;
            if (this.value) {
                if (this.value >= this.taskList[this.taskList.length - 1].value) {
                    const totalWidth = this.widthDistance.reduce((a, b) => a + b, 0);
                    this.barWidth = totalWidth + 2;
                    this.currentIndex = this.taskList.length - 1;
                }
                else {
                    let index = -1;
                    let beforeTotalWidth = 0;
                    for (let i = 0; i < this.taskList.length; i++) {
                        if (this.value > this.taskList[i].value) {
                            index = i;
                            beforeTotalWidth += this.widthDistance[i];
                        }
                    }
                    let nextValue = this.taskList[0].value;
                    let previousValue = 0;
                    if (index >= 0) {
                        nextValue = this.taskList[index + 1].value;
                        previousValue = this.taskList[index].value;
                    }
                    let width = ((this.value - previousValue) / (nextValue - previousValue)) * this.widthDistance[index + 1];
                    if (index >= 0) {
                        width = Math.max(this.exceedIconWidth, width);
                    }
                    else {
                        width = Math.max(10, width);
                    }
                    this.barWidth = width + beforeTotalWidth;
                    this.currentIndex = index;
                }
            }
            else {
                this.barWidth = 0;
            }
            if ($processBar) {
                $processBar.style.width = `${this.barWidth}px`;
            }
            this.setIconStatus();
        },
        setIconStatus() {
            this.iconStatus = Array.from({ length: this.taskList.length }).fill(0);
            for (const [index, task] of this.taskList.entries()) {
                this.iconStatus[index] = task.value <= this.value;
            }
        },
    },
};
</script>

<style lang="less" scoped>
.tab2-task-progress-process {
    overflow-x: scroll;
    position: relative;
    height: 55px;
}

.process {
    margin-top: 22px;
    height: 6px;
    border-radius: 3px;
    position: relative;
    .process-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #47518b;
        border-radius: 3px;
        z-index: 30;
    }
    .process-bar {
        position: absolute;
        left: 0;
        top: 0;
        z-index: 100;
        height: 100%;
        width: 0;
        background: #fff261;
        border-radius: 3px;
    }
}

.task-list {
    display: flex;
    flex-direction: row;
    position: absolute;
    left: 0;
    top: 0;
    .task-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 6.5px;
        &:last-of-type {
            margin-right: 0;
        }
        &:first-of-type {
            margin-left: 5px;
        }
        .exp {
            .pic-bg(url('images/<EMAIL>'), 45px, 15px);
            position: relative;
            .exp-value {
                position: absolute;
                left: 10px;
                top: 0;
                height: 100%;
                width: 35px;
                font-size: 11px;
                font-family:
                    Source Han Sans CN,
                    Source Han Sans CN-Medium;
                font-weight: 500;
                font-style: italic;
                text-align: center;
                color: #ffffff;
                line-height: 16px;
                letter-spacing: -0.22px;
            }
        }
        .task-icon {
            margin: 2px 0 3px 0;
            position: relative;
            z-index: 100;
            width: 15px;
            height: 15px;
            background: #47518b;
            border-radius: 50%;
            &.active {
                background: green;
            }
        }
        .value {
            font-size: 13px;
            font-family:
                Alibaba PuHuiTi 2,
                Alibaba PuHuiTi 2-65 Medium;
            font-weight: normal;
            font-style: italic;
            text-align: center;
            color: #4e7df1;
            line-height: 15px;
            letter-spacing: -0.26px;
        }
    }
}
</style>
