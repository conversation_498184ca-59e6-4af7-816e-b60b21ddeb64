<template>
    <div
        :class="{ 'has-time-text': task.startTime && task.endTime }"
        class="task">
        <div
            v-if="task.timeText"
            class="time">
            {{ task.timeText }}
        </div>
        <div
            v-if="hasStatusButton"
            class="task-status absolute right-[9px] top-[5.5px] z-100"
            :class="{
                'done': task.status,
                'todo-today': !task.status && !task.isDone,
                'todo-tomorrow': !task.status && task.isDone,
            }"
            @click="doTask">
        </div>
        <tab2-task-progress
            v-if="task.showType === TASKS_SHOW_TYPE.LONG_PROGRESS_BAR"
            :task="task" />
        <tab2-task-common
            v-else
            :task="task" />
    </div>
</template>

<script setup>
import { TASKS_SHOW_TYPE } from '@/stores/modules/use-tasks-store';

const props = defineProps({
    task: {
        type: Object,
        required: true,
    },
});

const hasStatusButton = computed(() => props.task.func || props.task.isDone || props.task.status);

async function doTask() {
    if (props.task.status || props.task.isDone) {
        return;
    }
    if (typeof props.task.func === 'function') {
        await props.task.func();
    }
}
</script>

<style lang="less" scoped>
.task {
    position: relative;
    margin-top: 9px;
    .task-status {
        &.todo-today {
            .pic-bg(url('images/<EMAIL>'), 100px, 32px);
        }
        &.todo-tomorrow {
            .pic-bg(url('images/<EMAIL>'), 100px, 32px);
        }
        &.done {
            .pic-bg(url('images/<EMAIL>'), 100px, 32px);
        }
    }
    .time {
        .pic-bg(url('images/<EMAIL>'), 221.5px, 25px);
        .flex-center();
        padding-left: 62px;
        padding-bottom: 4px;
        position: absolute;
        left: 0;
        top: -28px;
        font-size: 11px;
        font-family:
            Alibaba PuHuiTi 2,
            Alibaba PuHuiTi 2-75 SemiBold;
        font-weight: normal;
        color: #fffce0;
        line-height: 15px;
        letter-spacing: -0.24px;
    }
    &.has-time-text {
        margin-top: 40px;
    }
}
</style>
