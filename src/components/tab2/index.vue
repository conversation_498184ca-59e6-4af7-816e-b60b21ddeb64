<template>
    <div class="tab2">
        <tab2-tabs />
        <div class="tasks">
            <tab2-task
                v-for="(task, index) in tasksStore.tasks"
                :key="`${tasksStore.currentTasksTab}-${task.name}-${index}`"
                :task="task"
            />
            <div
                :class="{ shake: boxStore.canTakeBox }"
                class="icon-box"
                @click="boxStore.takeBox"></div>
        </div>
    </div>
</template>

<script setup>
import useBoxStore from '@/stores/modules/use-box-store';
import useTasksStore, { TABS } from '@/stores/modules/use-tasks-store';
import useLoading from '@/hooks/use-loading';

const tasksStore = useTasksStore();
const boxStore = useBoxStore();

useLoading(computed(() => tasksStore.loading.room || tasksStore.loading.tasks || boxStore.loading.take));

onBeforeMount(boxStore.roll);
onBeforeUnmount(boxStore.cancelRoll);
onMounted(async () => {
    await tasksStore.setCurrentTasksTab(TABS.DAILY);
    await tasksStore.queryTasks();
});
</script>

<style lang="less" scoped>
.tab2 {
    width: 375px;
    .tasks {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 6.5px 0 20px 0;
        width: 375px;
        min-height: 448px;
        background: #d2dae2;
    }
    .icon-box {
        .pic-bg(url('images/<EMAIL>'), 70px, 70px);
        position: fixed;
        right: 1px;
        bottom: 5.5px;
        z-index: 200;
    }
}
</style>
