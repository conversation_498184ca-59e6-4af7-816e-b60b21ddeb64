<template>
    <div class="shop-item">
        <div
            class="reward-item flex-center">
            <img
                :src="reward.imageUrl"
                alt=""
                class="h-[94px] w-[94px]">
        </div>

        <div class="quantity-control">
            <motion-press
                class="reduce"
                :style="{
                    filter: quantity <= 1 ? 'grayscale(100%)' : 'none',
                }"
                @touchstart="onTouchStart($event, 'reduce')"
                @touchmove="onTouchMove($event)"
                @touchend="onTouchEnd($event, 'reduce')"
                @press="pcClick('reduce')"
            >
                -
            </motion-press>

            <input
                ref="quantityInput"
                v-model="quantity"
                class="quantity"
                type="number"
                min="1"
                @input="handleQuantityInput"
                @blur="handleQuantityBlur"
            >
            <motion-press
                class="plus"
                :style="{
                    filter: quantity >= maxQuantity ? 'grayscale(100%)' : 'none',
                }"
                @touchstart="onTouchStart($event, 'plus')"
                @touchmove="onTouchMove($event)"
                @touchend="onTouchEnd($event, 'plus')"
                @press="pcClick('plus')">
                +
            </motion-press>
        </div>
        <div
            v-if="reward.limit"
            class="absolute left-1/2 top-175 text-[16px] text-[#FFF99C] -translate-x-1/2">
            单次上限{{ reward.limit }}
        </div>

        <motion-press
            class="exchange flex-center"
            @press="exchangeReward"
        >
            <img
                src="@/assets/img/<EMAIL>"
                class="mr-[4px] h-[26px] w-[26px]" />
            {{ reward.coin * quantity }}
        </motion-press>
        <div class="mt-[22px] flex-center text-[16px] text-[#E5E5E9]">
            我的
            <img
                src="@/assets/img/<EMAIL>"
                class="mx-[4px] h-[26px] w-[26px]" />
            <span class="text-[#FFE369] font-bold">{{ tickets }}</span>个
        </div>
    </div>
</template>

<script setup>
import to from 'await-to-js';
import useExchangeStore from './use-exchange-store';

const props = defineProps({
    reward: {
        type: Object,
        default: () => {},
    },
});

const { exchange, update, tickets } = storeToRefs(useExchangeStore());

const inpc = ref(parseUrlQuery().os_type === 'pc');

const quantityInput = ref(null);
// 商品兑换数量
const quantity = ref(1);
const touchClock = ref(false); // 是否可以触发长按
const toucheX = ref(0); // touchstart的x坐标
const toucheY = ref(0);// touchstart的y坐标
const toucheMoveX = ref(0);// touchmove的x坐标
const toucheMoveY = ref(0);// touchmove的y坐标
const toucheStartTime = ref(0); // 记录触摸开始的时间戳
const timeOutEvent = ref(null); // 存储长按定时器的引用
const httpClock = ref(false);
const drawStatus = ref('normal'); // normal未点击 reduce减少 plus增加

const exchangeStore = useExchangeStore();

// 最大星光值可兑换数量
const maxStar = computed(() => {
    const curProduct = props.reward;

    const star = curProduct?.coin || 0;
    if (star === 0)
        return Infinity;

    const maxByStar = Math.floor(exchangeStore.tickets / star);

    return maxByStar;
});

// 最大库存
const maxStock = computed(() => {
    const curProduct = props.reward;

    return curProduct?.limit === 0 ? Infinity : curProduct?.limit - curProduct?.redeemedNum;
});

// 最大可兑换数量
const maxQuantity = computed(() => {
    return Math.min(maxStar.value, (props.reward.limit || Number.POSITIVE_INFINITY));
});

// 处理输入框输入
const handleQuantityInput = (e) => {
    // 只保留数字
    // eslint-disable-next-line regexp/prefer-d
    const value = e.target.value.replace(/[^0-9]/g, '');
    quantity.value = value ? Number.parseInt(value) : '';
    if (!value) {
        quantity.value = '';
    }
    else if (value < 1) {
        quantity.value = 1;
    }
    else if (value > maxQuantity.value) {
        quantity.value = maxQuantity.value;
    }
    else {
        quantity.value = Number.parseInt(value);
    }
};

// 处理输入框失去焦点
const handleQuantityBlur = () => {
    if (!quantity.value) {
        quantity.value = 1;
    }
};

const exchangeReward = () => {
    if (httpClock.value)
        return;
    httpClock.value = true;
    setTimeout(async () => {
        if (quantity.value > maxStar.value) {
            httpClock.value = false;
            showToast('庆典积分不足！');
            return;
        }
        // 兑换逻辑
        const toast = showLoading();
        const [err, res] = await exchange({
            resourceId: props.reward.id,
            num: quantity.value,
        });
        toast.close();
        httpClock.value = false;
        if (err) {
            showToast('服务器开小差了，请稍后重试~');
            return;
        }
        if (res?.code === 0) {
            showToast('兑换成功');
            quantity.value = 1;
        }
        update();
    }, 300);
};

// 判断是否可以兑换
function canExchange(count) {
    return count <= maxQuantity.value;
}

/**
 * 增加兑换数量
 */
// 用于存储定时器ID，以便后续清除定时器
let intervalId = null;

/**
 * 增加兑换数量，如果增加后的数量在可兑换范围内，则更新数量
 */
const increaseQuantity = () => {
    // 检查增加后的数量是否可兑换
    if (canExchange(quantity.value + 1)) {
        // 若可兑换，则增加兑换数量
        quantity.value++;
    }
    else {
        if (quantity.value + 1 > maxStock.value) {
            showToast('已达到兑换上限！');
        }
        else if (quantity.value + 1 > maxStar.value) {
            showToast('庆典积分不足！');
        }
    }
};

/**
 * 开始持续增加兑换数量，先立即增加一次，之后每隔100毫秒尝试增加一次
 */
const startIncreasing = () => {
    // 清除之前的定时器，防止重复触发
    clearInterval(intervalId);
    // 立即增加一次兑换数量
    increaseQuantity();
    // 检查是否还能继续增加数量
    if (!canExchange(quantity.value + 1)) {
        return;
    }
    // 设置定时器，每隔100毫秒尝试增加一次兑换数量
    intervalId = setInterval(() => {
        // 检查增加后的数量是否可兑换
        if (canExchange(quantity.value + 1)) {
            // 若可兑换，则增加兑换数量
            quantity.value++;
        }
        else {
            // 若不可兑换，则清除定时器
            clearInterval(intervalId);
        }
    }, 100);
};

/**
 * 停止持续增加兑换数量，清除定时器
 */
const stopIncreasing = () => {
    // 清除定时器，停止增加数量
    clearInterval(intervalId);
};

/**
 * 减少兑换数量，最小数量为1
 */
const decreaseQuantity = () => {
    // 检查当前数量是否大于1
    if (quantity.value > 1) {
        // 若大于1，则减少兑换数量
        quantity.value--;
    }
    else {
        showToast('最少兑换1个~');
    }
};

/**
 * 减少兑换数量
 * 最小数量为1
 */
/**
 * 开始持续减少兑换数量，先立即减少一次，之后每隔100毫秒尝试减少一次
 */
const startDecreasing = () => {
    // 清除之前的定时器，防止重复触发
    clearInterval(intervalId);
    // 立即减少一次兑换数量
    decreaseQuantity();
    // 检查是否还能继续减少数量
    if (quantity.value <= 1) {
        return;
    }
    // 设置定时器，每隔100毫秒尝试减少一次兑换数量
    intervalId = setInterval(() => {
        // 检查当前数量是否大于1
        if (quantity.value > 1) {
            // 若大于1，则减少兑换数量
            quantity.value--;
        }
        else {
            // 若等于1，则清除定时器
            clearInterval(intervalId);
        }
    }, 100);
};

/**
 * 停止持续减少兑换数量，清除定时器
 */
const stopDecreasing = () => {
    // 清除定时器，停止减少数量
    clearInterval(intervalId);
};

// 长按触发兑换数量的增减操作
/**
 * 触摸开始事件处理函数，用于处理长按开始逻辑
 * @param {Event} e - 触摸事件对象
 * @param {string} eventType - 事件类型，'reduce' 表示减少，'plus' 表示增加
 */
const onTouchStart = (e, eventType) => {
    // 阻止系统默认事件，防止页面滚动等操作
    e.preventDefault();
    // 如果 touchClock 为 true，则不执行后续逻辑
    if (touchClock.value)
        return;
    // 如果 drawStatus 不是 'normal'，则不执行后续逻辑
    if (drawStatus.value !== 'normal') {
        return;
    }
    // 记录触摸开始时的 x 坐标
    toucheX.value = e.targetTouches[0].screenX;
    // 记录触摸开始时的 y 坐标
    toucheY.value = e.targetTouches[0].screenY;
    // 初始化触摸移动时的 x 坐标
    toucheMoveX.value = e.targetTouches[0].screenX;
    // 初始化触摸移动时的 y 坐标
    toucheMoveY.value = e.targetTouches[0].screenY;
    // 记录触摸开始的时间戳
    toucheStartTime.value = new Date().getTime();
    // 开启定时器前先清除定时器，防止重复触发
    if (timeOutEvent.value) {
        clearTimeout(timeOutEvent.value);
    }
    // 更新 drawStatus 为当前事件类型
    drawStatus.value = eventType;

    // 设置定时器，700 毫秒后执行长按操作
    timeOutEvent.value = setTimeout(() => {
        if (eventType === 'reduce') {
            // 若事件类型为 'reduce'，则开始持续减少兑换数量
            startDecreasing();
        }
        else if (eventType === 'plus') {
            // 若事件类型为 'plus'，则开始持续增加兑换数量
            startIncreasing();
        }
    }, 700);
};

/**
 * 触摸移动事件处理函数，用于更新触摸移动的坐标
 * @param {Event} e - 触摸事件对象
 */
const onTouchMove = (e) => {
    // 如果 touchClock 为 true，则不执行后续逻辑
    if (touchClock.value)
        return;
    // 获取触摸移动时的 x 坐标
    const moveX = e.targetTouches[0].screenX;
    // 获取触摸移动时的 y 坐标
    const moveY = e.targetTouches[0].screenY;
    // 更新触摸移动时的 x 坐标
    toucheMoveX.value = moveX;
    // 更新触摸移动时的 y 坐标
    toucheMoveY.value = moveY;
};

/**
 * 触摸结束事件处理函数，用于处理点击和长按结束逻辑
 * @param {Event} e - 触摸事件对象
 * @param {string} eventType - 事件类型，'reduce' 表示减少，'plus' 表示增加
 */
const onTouchEnd = (e, eventType) => {
    // 如果 touchClock 为 true，则不执行后续逻辑
    if (touchClock.value)
        return;
    const maxMoveDistance = 10; // 最大移动距离阈值;
    // 如果是 500 毫秒以内的，必须 xy 轴不动才算点击
    if (new Date().getTime() - toucheStartTime.value < 500) {
        if (Math.abs(toucheX.value - toucheMoveX.value) < maxMoveDistance && Math.abs(toucheY.value - toucheMoveY.value) < maxMoveDistance) {
            if (eventType === 'reduce') {
                // 若事件类型为 'reduce'，则减少一次兑换数量
                decreaseQuantity();
            }
            else if (eventType === 'plus') {
                // 若事件类型为 'plus'，则增加一次兑换数量
                increaseQuantity();
            }
        }
    }
    else if (drawStatus.value === 'reduce') {
        // 若 drawStatus 为 'reduce'，则停止持续减少兑换数量
        stopDecreasing();
    }
    else if (drawStatus.value === 'plus') {
        // 若 drawStatus 为 'plus'，则停止持续增加兑换数量
        stopIncreasing();
    }
    // 将 drawStatus 重置为 'normal'
    drawStatus.value = 'normal';
    // 清除定时器，结束长按逻辑
    if (timeOutEvent.value) {
        clearTimeout(timeOutEvent.value);
    }
    // 若手指离开屏幕，时间小于我们设置的长按时间，则为点击事件
};

/**
 * 处理 PC 端点击事件，根据事件类型增加或减少兑换数量
 * @param {string} eventType - 事件类型，'reduce' 表示减少，'plus' 表示增加
 */
function pcClick(eventType) {
    // 检查是否为 PC 端
    if (inpc.value) {
        if (eventType === 'reduce') {
            // 若事件类型为 'reduce'，则减少一次兑换数量
            decreaseQuantity();
        }
        else if (eventType === 'plus') {
            // 若事件类型为 'plus'，则增加一次兑换数量
            increaseQuantity();
        }
    }
}

// 变成可以兑换最大值
function toAddAll() {
    if (maxQuantity.value > 999) {
        quantity.value = 999;
    }
    else if (maxQuantity.value > 1) {
        if (quantity.value === maxQuantity.value) {
            showToast('已达到兑换上限！');
        }
        else {
            quantity.value = maxQuantity.value;
        }
    }
    else if (maxStock.value < 1) {
        showToast('已达到兑换上限！');
    }
    else if (maxStar.value < 1) {
        showToast('庆典积分不足！');
    }
}
</script>

<style lang="less" scoped>
.shop-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    padding: 0 6px;
    margin-bottom: 7px;
    .reward-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        background-image: url('@/assets/img/<EMAIL>');
        background-size: 100% 100%;
        width: 112.5px;
        height: 112.5px;
    }

    .quantity-control {
        display: flex;
        align-items: center;
        background-image: url('@/assets/img/<EMAIL>');
        background-size: 100% 100%;
        width: 129px;
        height: 28px;
        margin-top: 23px;
        z-index: 2;
        position: relative;
        .reduce,
        .plus {
            z-index: 2;
            .flex-center();
            height: 28px;
            width: 28px;
            font-size: 14px;
            font-family:
                Alibaba PuHuiTi 2,
                Alibaba PuHuiTi 2-75 SemiBold;
            font-weight: normal;
            text-align: center;
            color: #72b4ff;
            letter-spacing: -0.28px;
        }

        .quantity {
            width: 90px;
            height: 28px;
            background: transparent;
            line-height: 28px;
            text-align: center;
            font-size: 11px;
            color: #fff;
            border: none;
        }

        .add-all {
            // background-image: url('@/assets/img/<EMAIL>');
            background-size: 100% 100%;
            width: 31.5px;
            height: 17px;
            margin-left: 6px;
        }
    }

    .exchange {
        background-image: url('@/assets/img/<EMAIL>');
        background-size: 100% 100%;
        width: 193px;
        height: 48px;
        margin-top: 37px;
        font-size: 20px;
        color: #fffcea;
    }

    .limit {
        font-size: 9px;
        color: #fff7de;
        margin-top: 2px;
    }
}
</style>
