<template>
    <popup-container
        v-model:show="isShow"
        :close-on-click-overlay="true"
        class="z-10000"
    >
        <div class="draw-record-modal">
            <div class="h-[29.5px] w-[375px] flex items-center px-[20px] text-left text-[14px] text-[#E5E5E9]">
                <div class="w-[30%]">时间</div>
                <div class="w-[20%] text-center">来源</div>
                <div class="w-[40%] text-center">奖励内容</div>
                <div class="w-[20%] text-center">消耗积分</div>
            </div>
            <div
                ref="wrapper"
                class="box-border h-[190px] w-full flex-1 overflow-x-hidden overflow-y-auto"
                @scroll="handleScroll"
            >
                <template v-if="recordStore.list?.length">
                    <div
                        v-for="(item, index) of recordStore.list"
                        :key="index"
                        class="item bg-default mx-auto box-border min-h-[54px] flex break-all px-[20px] pt-[10px] text-[13px] text-[#E5E5E9] leading-[18px]"
                    >
                        <div class="w-[30%]">{{ formatTime(item?.time) }}</div>
                        <div class="w-[20%] text-center">{{ item.extraParam.lotteryType === 1 ? '宝箱' : '兑换' }}</div>
                        <div class="w-[40%]">{{ formatName(item?.reward) }}</div>
                        <div class="w-[20%] text-center">{{ item.extraParam.lotteryType === 1 ? item.extraParam.exchangeNum * DRAW_COIN : item.extraParam.exchangeNum * (exchangeRewardList.find(d => d.id === item.reward[0].id)?.coin || 0) }}</div>
                    </div>
                </template>
                <div
                    v-else
                    class="bg-default relative mx-auto h-[206px] w-[100%] flex flex-col items-center pt-[94px] text-[16px] text-[#E5E5E9]"
                    :style="{ backgroundImage: `url(${requireImg('<EMAIL>')})` }"
                >
                    暂无记录
                </div>
            </div>
        </div>
    </popup-container>
</template>

<script setup>
import { ref } from 'vue';
import dayjs from 'dayjs';
import { throttle } from 'lodash-es';
import useDrawRecords from './use-draw-records';
import { exchangeRewardList } from './use-exchange-store';
import { getRewardInfo } from '@/auto-imports/my-utils';

const DRAW_COIN = 10;

const wrapper = ref();

const recordStore = useDrawRecords();
const isShow = ref(false);
useEventBus('draw-record-modal').on((params) => {
    console.log('open');

    isShow.value = params.show;
});
const closeDialog = () => {
    isShow.value = false;
};
const handleScroll = throttle(() => {
    const scrollWrap = wrapper.value;
    if (scrollWrap.scrollTop + scrollWrap.clientHeight >= scrollWrap.scrollHeight - 30) {
        console.log('下一页');
        recordStore.nextPage();
    }
}, 1000);

watch(() => isShow.value, (value) => {
    if (!value) {
        recordStore.reset();
    }
    else {
        recordStore.nextPage();
    }
});

const formatName = (rewardList) => {
    let res = '';
    rewardList.forEach((item) => {
        const reward = getRewardInfo(item.id);
        res += `${reward.name}${reward.mark === '包裹' ? reward.price + reward.special_type : reward.mark}*${item.num || item.time}${reward.unit} `;
    });
    return res;
};

const formatTime = (s) => {
    if (!s)
        return '';
    try {
        const t = dayjs.unix(s).tz('Asia/Shanghai');
        return t.format('MM月DD日 HH:mm');
    }
    catch (error) {
        //
    }
    return '';
};
</script>

<style lang="less" scoped>
.draw-record-modal {
    width: 375px;
    height: 630px;
    background-image: url('@/assets/img/<EMAIL>');
    background-size: 100% 100%;
    font-family: PingFangSC-Medium;
    text-align: center;
    padding: 66px 0;
}

.item {
    font-family:
        Alibaba PuHuiTi,
        Alibaba PuHuiTi-Medium;
    font-size: 12px;
    font-weight: 500;
    text-align: left;
    background-color: #244384;

    &:nth-child(2n) {
        background-color: #234284;
    }
}

.btn {
    display: flex;
    justify-content: center;
    position: absolute;
    bottom: 30px;
    left: 87px;
}
</style>
