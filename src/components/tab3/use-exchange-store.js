import { defineStore } from 'pinia';
import { reactive, toRefs } from 'vue';
import { exchangeReward as exchangeApi, lotteryData as getTickets, lottery as lotteryApi } from '@/api/index';
import { rewardMap } from '@/config/reward';

import { getRewardInfo } from '@/auto-imports/my-utils';

const DRAW_NUM = 10;

const drawRewardList = [
    {
        id: 'BOX_Gift1',
        ...getRewardInfo('BOX_Gift1'),
        num: 1,
        remark2: '100豆',
    },
    {
        id: 'BOX_Gift2',
        ...getRewardInfo('BOX_Gift2'),
        num: 1,
        remark2: '1000豆',
    },
    {
        id: 'BOX_Gift3',
        ...getRewardInfo('BOX_Gift3'),
        num: 1,
        remark2: '5000豆',
    },
    {
        id: 'BOX_Gift4',
        ...getRewardInfo('BOX_Gift4'),
        num: 1,
        remark2: '10000豆',
    },
    {
        id: 'BOX_Gift5',
        ...getRewardInfo('BOX_Gift5'),
        num: 1,
        remark2: '52000豆',
    },
    {
        id: 'BOX_Gift6',
        ...getRewardInfo('BOX_Gift6'),
        num: 1,
        remark2: '131400豆',
    },
    {
        id: 'BOX_Gift7',
        ...getRewardInfo('BOX_Gift7'),
        num: 1,
        remark2: '100红钻',
    },
    {
        id: 'BOX_Gift8',
        ...getRewardInfo('BOX_Gift8'),
        num: 1,
        remark2: '1000红钻',
    },
    {
        id: 'BOX_CS1',
        ...getRewardInfo('BOX_CS1'),
        num: 1,
    },
    {
        id: 'BOX_CS2',
        ...getRewardInfo('BOX_CS2'),
        num: 1,
    },
    {
        id: 'BOX_CS3',
        ...getRewardInfo('BOX_CS3'),
        num: 1,
    },
];

export const exchangeRewardList = [
    {
        id: 'EX_GUR1',
        ...getRewardInfo('EX_GUR1'),
        num: 1,
        remark2: '礼物赠送权',
        coin: 5,
    },
    {
        id: 'EX_GUR2',
        ...getRewardInfo('EX_GUR2'),
        num: 1,
        remark2: '礼物赠送权',
        coin: 10,
    },
    {
        id: 'EX_Card1',
        ...getRewardInfo('EX_Card1'),
        num: 1,
        remark2: '贵族体验卡',
        coin: 20,
        limit: 1,
    },
    {
        id: 'EX_Card2',
        ...getRewardInfo('EX_Card2'),
        num: 1,
        remark2: '贵族体验卡',
        coin: 30,
        limit: 1,
    },
    {
        id: 'EX_CS1',
        ...getRewardInfo('EX_CS1'),
        num: 1,
        remark2: '麦位框',
        coin: 5,
        limit: 60,
    },
    {
        id: 'EX_CS2',
        ...getRewardInfo('EX_CS2'),
        num: 1,
        remark2: '坐骑',
        coin: 5,
        limit: 60,
    },
    {
        id: 'EX_CS3',
        ...getRewardInfo('EX_CS3'),
        num: 1,
        remark2: '主页飘',
        coin: 5,
        limit: 60,
    },
    {
        id: 'EX_CS4',
        ...getRewardInfo('EX_CS4'),
        num: 1,
        remark2: '个人铭牌',
        coin: 5,
    },
    {
        id: 'EX_CS5',
        ...getRewardInfo('EX_CS5'),
        num: 1,
        remark2: '个人房间背景',
        coin: 5,
    },
];

const useExchangeStore = defineStore('exchange', () => {
    let state = reactive({
        drawRewardList,
        exchangeRewardList,
        tickets: 0,
        rewardInfoMap: {},
    });

    const update = async (payload = {}) => {
        try {
            const [{ code, data }] = await getTickets(payload);
            state = Object.assign(state, data);
            state.tickets = data.shell;
            return { code, data };
        }
        catch (e) {
            return false;
        }
    };

    // 兑换其他奖励
    const exchange = async (payload = {}) => {
        try {
            const [{ code, data }] = await exchangeApi(payload);
            if (code === 0) {
                showToast('兑换成功');
            }
            await update();
            return { code, data };
        }
        catch (e) {
            return false;
        }
    };

    const lottery = async (payload = {}) => {
        try {
            const [{ code, data }] = await lotteryApi(payload);
            return { code, data };
        }
        catch (e) {
            return false;
        }
    };

    return {
        ...toRefs(state),
        exchange,
        update,
        lottery,
        DRAW_NUM,
    };
});

export default useExchangeStore;
