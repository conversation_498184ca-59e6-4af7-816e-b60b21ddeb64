<template>
    <modal-container
        v-model:show="isShow"
        :close-on-click-overlay="true"
        class="z-10000">
        <div
            class="common-modal-container"
        >
            <div class="absolute left-1/2 top-10 transform whitespace-nowrap text-[24px] text-[#FFFFFF] -translate-x-1/2"> 兑换{{ info.name }}</div>
            <shop-item :reward="info" />
        </div>
    </modal-container>
</template>

<script setup>
const isShow = ref(false);
const info = ref({});
useEventBus('exchange-confirm-modal').on((params) => {
    isShow.value = params.show;
    info.value = params.info;
});
</script>

<style lang="less" scoped>
.common-modal-container {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    align-items: center;
    background-image: url('@/assets/img/<EMAIL>');
    background-size: 100% 100%;
    width: 375px;
    height: 397px;
    padding: 66px 20px 0;
}
</style>
