<template>
    <div class="draw overflow-hidden">
        <div class="absolute top-[6px] w-[100%] flex items-center px-22 text-[20px] text-[#E5E5E9] leading-[20px]">
            我的 <img
                src="@/assets/img/<EMAIL>"
                class="h-[26px] w-[26px]"><span class="text-[#FFE369]">{{ store.tickets }}</span> 个
            <div
                class="ml-auto text-[14px] text-[#A9F7FE] leading-[14px]"
                @click="openExchangeRecord">
                兑换记录>
            </div>
        </div>

        <div
            class="bg-default relative h-[185px] w-[350.5px]"
            :style="{ backgroundImage: `url(${requireImg('<EMAIL>')})` }"
        >
            <div class="absolute left-6 top-22 grid grid-cols-3 h-160 w-210 flex-wrap overflow-y-auto">
                <div
                    v-for="(reward) of store.exchangeRewardList"
                    :key="reward.resource_id"
                    class="mb-[10px] flex flex-shrink-0 flex-col items-center"

                >
                    <div
                        class="bg-default relative h-[61px] w-[61px] flex flex-center flex-col"
                        :style="{ backgroundImage: `url(${requireImg('<EMAIL>')})` }"
                    >
                        <img
                            class="h-[90%] w-[90%] object-contain"
                            :src="reward.imageUrl"
                        />
                        <div
                            class="text-shadow-gray absolute bottom-[0px] right-[1px] mx-auto flex-center flex-shrink-0 text-center text-[12px] text-[#fff] leading-12"
                        >
                            {{ reward.num }}{{ reward.unit }}
                        </div>
                    </div>
                    <div class="reward-name mt-[7px]">{{ reward.name }}</div>
                    <div class="reward-mark mt-[2px]">
                        {{ (reward?.mark !== '包裹' ? reward.mark : reward.remark2) }}
                    </div>
                </div>
            </div>
            <img
                src="@/assets/img/<EMAIL>"
                class="absolute right-20 top-30 h-[94px] w-[94px]"
                alt="">
            <div class="absolute right-40 top-10 text-[15px] text-[#423698] font-bold">海洋秘宝</div>
            <div
                class="bg-default absolute right-[12px] top-[140px] h-[32px] w-[100px] flex flex-center text-[15px] text-[#FFFCEA]"
                :style="{ backgroundImage: `url(${requireImg('<EMAIL>')})` }"
                @click="doDraw"
            >
                <img
                    src="@/assets/img/<EMAIL>"
                    class="mr-[4px] h-[26px] w-[26px]" />
                {{ store.DRAW_NUM }}次
            </div>
        </div>

        <div class="grid grid-cols-3 mt-15 w-375 flex-wrap px-12">
            <div
                v-for="(reward) of store.exchangeRewardList"
                :key="reward.resource_id"
                class="bg-default mb-[15px] h-[149px] w-[111.5px] flex flex-shrink-0 flex-col items-center"
                :style="{ backgroundImage: `url(${requireImg('<EMAIL>')})` }"
            >
                <div class="mt-6 text-[13px] text-[#FFFFFF] leading-[13px]"> {{ reward.name }} </div>
                <div
                    class="bg-default relative mt-14 h-[64px] w-[64px] flex flex-center flex-col"
                >
                    <img
                        class="h-[90%] w-[90%] object-contain"
                        :src="reward.imageUrl"
                    />
                    <div
                        class="text-shadow-gray absolute right-[1px] top-[50px] mx-auto flex-center flex-shrink-0 text-center text-[12px] text-[#fff] leading-15"
                    >
                        {{ reward.num }}{{ reward.unit }}
                    </div>
                </div>

                <div
                    :class="[(store.tickets < reward.coin) && 'grayscale-[70]']"
                    class="bg-default mt-[8px] h-[32px] w-[100px] flex-center text-[18px] text-[#FFF5BE]"
                    :style="{ backgroundImage: `url(${requireImg('<EMAIL>')})` }"
                    @click="openExchange(reward)"
                >
                    <img
                        src="@/assets/img/<EMAIL>"
                        class="mr-[4px] h-[26px] w-[26px]" />
                    {{ reward.coin }}
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import useExchangeStore from './use-exchange-store';
import { stopDoubleClick } from '@/utils';

const store = useExchangeStore();

const vm = ref();

function openExchange(params) {
    if (!stopDoubleClick(vm, 2000))
        return;
    if (store.tickets < params.coin) {
        showToast('贝壳不足');
        return;
    }
    useEventBus('exchange-confirm-modal').emit({ show: true, info: params });
}

function openExchangeRecord() {
    useEventBus('draw-record-modal').emit({ show: true });
}

const isLoading = ref(false);

async function doDraw() {
    try {
        if (!stopDoubleClick(vm, 2000))
            return;
        if (isLoading.value) {
            return;
        }
        if (store.isEnd) {
            showToast('活动已结束！');
            return;
        }
        if (store.tickets < store.DRAW_NUM) {
            showToast('您贝壳不足,快去完成任务解锁机会吧~');
            return;
        }
        useEventBus('exchange-confirm-modal').emit({ show: true, info: {
            name: '海洋秘宝',
            coin: store.DRAW_NUM,
            imageUrl: requireImg('<EMAIL>'),
            id: 'draw',
        } });
    }
    catch (error) {
        isLoading.value = false;
    }
    finally {
        isLoading.value = false;
    }
}

onMounted(async () => {
    await store.update();
});
</script>

<style lang="less" scoped>
.draw {
    width: 375px;
    background: #244f8e;
    border-radius: 15px 15px 0px 0px;
    // background-image: url('@/assets/img/<EMAIL>');
    // background-size: 100% 100%;
    padding-top: 46px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow-y: auto;
    padding-bottom: 12px;
    position: relative;

    .close-btn {
        position: absolute;
        top: 2px;
        right: 10px;
        width: 40px;
        height: 40px;
    }

    .scroll-wrapper {
        width: 100%;
        height: 240px;
        margin: auto;
        box-sizing: border-box;
        overflow-y: auto;
        overflow-x: hidden;
        padding: 0px 7px 80px;
    }
}

.select {
    width: 88.5px;
    height: 37.5px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #ffffff;
    font-size: 15px;
    // background-image: url('@/assets/img/<EMAIL>');
    background-size: 100% 100%;
}

.selected {
    // background-image: url('@/assets/img/<EMAIL>');
    background-size: 100% 100%;
}

.reward-name,
.reward-mark {
    .one-line();
    text-align: center;
    font-size: 12px;
    color: #fff99c;
    line-height: 12.5px;
}
</style>
