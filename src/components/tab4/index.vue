<template>
    <div
        ref="scrollContainer"
        class="tab4-rank-list mb-100"
        :class="{ hall: rankStore.currentType === RANK_TYPE.TAB1 }"
        @scroll="handleScroll">
        <!-- 子导航 -->
        <sub-nav-tabs
            v-model="rankStore.currentType"
            :nav-config="navConfig"
            @change="handleTypeChange"
        />

        <!-- 榜单内容 -->
        <div
            v-if="rankStore.currentType === RANK_TYPE.TAB1"
            class="rank-content">
            <div class="w-full flex items-center justify-center text-13 text-[#FFFFFF]">
                开启榜单后送出1豆/在房1分钟=1榜单值 <img
                    class="ml-2 h-16 w-16"
                    src="@/assets/img/<EMAIL>"
                    alt="">
            </div>
            <!-- 错误状态 -->
            <div
                v-if="rankStore.error.hasError"
                class="error-state">
                <div class="error-icon">⚠️</div>
                <div class="error-message">{{ rankStore.error.message }}</div>
                <div
                    v-if="rankStore.error.canRetry"
                    class="retry-btn"
                    @click="handleRetry"
                >
                    重新加载
                </div>
            </div>

            <!-- 正常内容 -->
            <template v-else>
                <!-- 前三名 -->
                <tab4-top-three
                    v-if="rankStore.topThreeList.length > 0"
                    :list="rankStore.topThreeList"
                    :type="rankStore.currentType"
                />

                <!-- 普通排名列表 -->
                <div
                    v-if="rankStore.normalList.length > 0"
                    class="normal-list">
                    <tab4-rank-item
                        v-for="(item, index) in rankStore.normalList"
                        :key="`${rankStore.currentType}-${item.uid || item.id}-${index}`"
                        :item="item"
                        :rank="index + 4"
                        :type="rankStore.currentType"
                    />
                </div>

                <!-- 自己的排名 -->
                <div
                    v-if="!battlePassStore.hasBuySuperBattlePass"
                    class="bg-default fixed bottom-0 left-0 z-2 h-[87px] w-[375px] flex items-center px-20"
                    :style="{ backgroundImage: `url(${requireImg('<EMAIL>')})` }"
                >
                    <div>
                        <div class="text-left text-[20px] text-[#e8dec2] leading-[21px]">榜单未解锁</div>
                        <div class="text-left text-[14px] text-[#e5e5e5] leading-[21px]">完成豪华进阶井满级后即可解锁榜单</div>
                    </div>
                    <img
                        src="@/assets/img/<EMAIL>"
                        class="ml-auto h-[40px] w-[103px]"
                        alt=""
                        @click="useEventBus('modal-buy-battle-pass').emit({ show: true })">
                </div>
                <div
                    v-else-if="!levelStore.isReachMaxLevel"
                    class="bg-default fixed bottom-0 left-0 z-2 h-[87px] w-[375px] flex items-center px-20"
                    :style="{ backgroundImage: `url(${requireImg('<EMAIL>')})` }"
                >
                    <div>
                        <div class="text-left text-[20px] text-[#e8dec2] leading-[21px]">榜单未解锁</div>
                        <div class="text-left text-[14px] text-[#e5e5e5] leading-[21px]">通行证满级后即可解锁榜单</div>
                    </div>
                    <img
                        src="@/assets/img/<EMAIL>"
                        class="ml-auto h-[40px] w-[103px]"
                        @click="tabStore.setTabId(TABS.TAB1)">
                </div>
                <div
                    v-else-if="rankStore.hasSelfRank"
                    class="self-rank">
                    <tab4-rank-item
                        :item="rankStore.selfRank"
                        :rank="rankStore.selfRank.rank || rankStore.selfRank.ranking"
                        :type="rankStore.currentType"
                        :is-self="true"
                    />
                </div>

                <!-- 空状态 -->
                <div
                    v-if="rankStore.showEmptyState"
                    class="empty-state">
                    <div class="empty-text">暂无榜单数据</div>
                </div>

                <!-- 加载更多提示 -->
                <div
                    v-if="rankStore.loading.more"
                    class="loading-more">
                    <div class="loading-spinner"></div>
                    <span>加载中...</span>
                </div>

                <!-- 没有更多数据 -->
                <div
                    v-if="!rankStore.hasMore && rankStore.list.length > 0"
                    class="no-more">
                    <div class="no-more-line"></div>
                    <span>没有更多数据了</span>
                    <div class="no-more-line"></div>
                </div>
            </template>
        </div>
        <!-- mk r -->
    </div>
</template>

<script setup>
import { throttle } from 'lodash-es';
import SubNavTabs from './sub-nav-tabs.vue';
import Tab4TopThree from './tab4-top-three.vue';
import Tab4RankItem from './tab4-rank-item.vue';
import useRankStore, { RANK_TYPE } from './use-rank-store';
import { SUB_NAV_CONFIG } from './constants';
import useLoading from '@/hooks/use-loading';
import useBattlePassStore from '@/stores/modules/use-battle-pass-store';
import useLevelStore from '@/stores/modules/use-level-store';
import useTabStore from '@/stores/modules/use-tab-store';

const tabStore = useTabStore();
const battlePassStore = useBattlePassStore();
const levelStore = useLevelStore();

const rankStore = useRankStore();
const scrollContainer = ref(null);

// 导航配置
const navConfig = computed(() => [
    {
        value: RANK_TYPE.SEND,
        activeImg: SUB_NAV_CONFIG[0].activeImg,
        inactiveImg: SUB_NAV_CONFIG[0].inactiveImg,
        label: '送礼榜',
    },
    {
        value: RANK_TYPE.RECEIVE,
        activeImg: SUB_NAV_CONFIG[1].activeImg,
        inactiveImg: SUB_NAV_CONFIG[1].inactiveImg,
        label: '收礼榜',
    },
]);

useLoading(computed(() => rankStore.loading.list));

// 切换榜单类型
const handleTypeChange = async (type) => {
    await rankStore.switchRankType(type);
};

// 重试加载
const handleRetry = async () => {
    await rankStore.retryQuery();
};

// 滚动加载更多
const handleScroll = throttle(() => {
    const container = scrollContainer.value;
    if (!container || rankStore.loading.more || !rankStore.hasMore || rankStore.error.hasError)
        return;

    const { scrollTop, scrollHeight, clientHeight } = container;
    if (scrollTop + clientHeight >= scrollHeight - 50) {
        rankStore.loadMore();
    }
}, 300);

onMounted(async () => {
    await rankStore.queryRankList();
});
</script>

<style lang="less" scoped>
.tab4-rank-list {
    height: 100%;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
    background: url('@/assets/img/<EMAIL>') #244f8e no-repeat center top;
    background-size: 375px 448px;
    padding-top: 12px;

    .rank-content {
        margin-top: 15px;

        .error-state {
            text-align: center;
            padding: 60px 20px;

            .error-icon {
                font-size: 48px;
                margin-bottom: 15px;
            }

            .error-message {
                font-size: 14px;
                color: rgba(255, 255, 255, 0.8);
                margin-bottom: 20px;
                line-height: 1.4;
            }

            .retry-btn {
                display: inline-block;
                padding: 10px 20px;
                background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
                color: #fff;
                border-radius: 20px;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.3s ease;

                &:active {
                    transform: scale(0.95);
                    background: linear-gradient(135deg, #ff5252, #ff7979);
                }
            }
        }

        .normal-list {
            margin-top: 0px;
        }

        .self-rank {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);

            .self-rank-title {
                font-size: 16px;
                color: #fff;
                margin-bottom: 10px;
                text-align: center;
            }
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;

            .empty-icon {
                font-size: 48px;
                margin-bottom: 15px;
            }

            .empty-text {
                font-size: 16px;
                color: #fff;
                margin-bottom: 8px;
            }

            .empty-tip {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.6);
            }
        }

        .loading-more {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 15px;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.6);
            gap: 8px;

            .loading-spinner {
                width: 16px;
                height: 16px;
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-top: 2px solid rgba(255, 255, 255, 0.6);
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }
        }

        .no-more {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.4);
            gap: 15px;

            .no-more-line {
                flex: 1;
                height: 1px;
                background: rgba(255, 255, 255, 0.1);
            }
        }
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
.hall {
    background: url('@/assets/img/<EMAIL>') #244f8e no-repeat center top;
    background-size: 375px 448px;
}
</style>
