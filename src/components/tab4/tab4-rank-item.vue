<template>
    <div
        class="tab4-rank-item"
        :class="{ 'is-self': isSelf }">
        <div class="rank-number">{{ rank }}</div>

        <div class="user-info">
            <img
                :src="getAvatar(item.username)"
                class="avatar"
                @click="to<PERSON><PERSON>(item.username)" />
            <div class="nickname">{{ safeOmitTxt(item.nickname) }}</div>
        </div>

        <div class="rank-value">
            {{ formatValue(item.value) }}
        </div>
    </div>
</template>

<script setup>
import { formatNumber } from '@/utils';

const props = defineProps({
    item: {
        type: Object,
        required: true,
    },
    rank: {
        type: Number,
        required: true,
    },
    type: {
        type: Number,
        required: true,
    },
    isSelf: {
        type: Boolean,
        default: false,
    },
});

function formatValue(value) {
    return formatNumber(value || 0);
}
</script>

<style lang="less" scoped>
.tab4-rank-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    width: 356px;
    height: 82px;
    background-image: url('@/assets/img/<EMAIL>');
    background-size: 100%;
    margin-bottom: 8px;

    &.is-self {
        background-image: url('@/assets/img/<EMAIL>');
        background-size: 100%;
        width: 375px;
        height: 87px;
        position: fixed;
        bottom: 0;
        right: 0;
        z-index: 100;
        margin-bottom: 0;
    }

    .rank-number {
        width: 30px;
        font-size: 16px;
        font-weight: bold;
        color: #fff;
        text-align: center;
        flex-shrink: 0;
    }

    .user-info {
        display: flex;
        align-items: center;
        flex: 1;
        margin-left: 15px;
        min-width: 0;

        .avatar {
            width: 53px;
            height: 53px;
            border-radius: 50%;
            border: 1px solid rgba(255, 255, 255, 0.2);
            flex-shrink: 0;
        }

        .nickname {
            margin-left: 12px;
            font-size: 14px;
            color: #e5e5e5;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }

    .rank-value {
        font-size: 14px;
        color: #ffffff;
        font-weight: bold;
        flex-shrink: 0;
        margin-left: 15px;
    }
}
</style>
