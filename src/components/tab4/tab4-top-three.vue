<template>
    <div class="tab4-top-three">
        <div class="podium">
            <!-- 第二名 -->
            <div
                v-if="secondPlace"
                class="rank-item rank-2">
                <div class="rank-badge">2</div>
                <div class="avatar-container">
                    <img
                        :src="secondPlace.avatar"
                        class="avatar" />
                </div>
                <div class="nickname">{{ secondPlace.nickname }}</div>
                <div class="value">{{ formatValue(secondPlace.value) }}</div>
            </div>

            <!-- 第一名 -->
            <div
                v-if="firstPlace"
                class="rank-item rank-1">
                <div class="crown">👑</div>
                <div class="rank-badge">1</div>
                <div class="avatar-container">
                    <img
                        :src="firstPlace.avatar"
                        class="avatar" />
                </div>
                <div class="nickname">{{ firstPlace.nickname }}</div>
                <div class="value">{{ formatValue(firstPlace.value) }}</div>
            </div>

            <!-- 第三名 -->
            <div
                v-if="thirdPlace"
                class="rank-item rank-3">
                <div class="rank-badge">3</div>
                <div class="avatar-container">
                    <img
                        :src="thirdPlace.avatar"
                        class="avatar" />
                </div>
                <div class="nickname">{{ thirdPlace.nickname }}</div>
                <div class="value">{{ formatValue(thirdPlace.value) }}</div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { formatNumber } from '@/utils';

const props = defineProps({
    list: {
        type: Array,
        default: () => [],
    },
    type: {
        type: Number,
        required: true,
    },
});

const firstPlace = computed(() => props.list[0] || null);
const secondPlace = computed(() => props.list[1] || null);
const thirdPlace = computed(() => props.list[2] || null);

function formatValue(value) {
    return formatNumber(value || 0);
}
</script>

<style lang="less" scoped>
.tab4-top-three {
    .podium {
        display: flex;
        justify-content: center;
        align-items: flex-end;
        padding: 20px 0;
        gap: 10px;

        .rank-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;

            .rank-badge {
                width: 24px;
                height: 24px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                font-weight: bold;
                color: #fff;
                margin-bottom: 8px;
            }

            .crown {
                font-size: 20px;
                margin-bottom: 5px;
            }

            .avatar-container {
                position: relative;
                margin-bottom: 8px;

                .avatar {
                    border-radius: 50%;
                    border: 2px solid #fff;
                }
            }

            .nickname {
                font-size: 12px;
                color: #fff;
                margin-bottom: 4px;
                max-width: 80px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                text-align: center;
            }

            .value {
                font-size: 11px;
                color: rgba(255, 255, 255, 0.8);
                text-align: center;
            }

            // 第一名样式
            &.rank-1 {
                .rank-badge {
                    background: linear-gradient(135deg, #ffd700, #ffed4e);
                }

                .avatar {
                    width: 60px;
                    height: 60px;
                    border-color: #ffd700;
                }

                .nickname {
                    font-size: 14px;
                    font-weight: bold;
                }

                .value {
                    font-size: 12px;
                    color: #ffd700;
                    font-weight: bold;
                }
            }

            // 第二名样式
            &.rank-2 {
                .rank-badge {
                    background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
                    color: #666;
                }

                .avatar {
                    width: 50px;
                    height: 50px;
                    border-color: #c0c0c0;
                }
            }

            // 第三名样式
            &.rank-3 {
                .rank-badge {
                    background: linear-gradient(135deg, #cd7f32, #daa520);
                }

                .avatar {
                    width: 50px;
                    height: 50px;
                    border-color: #cd7f32;
                }
            }
        }
    }
}
</style>
