<template>
    <div class="tab4-top-three">
        <div class="podium">
            <!-- 第二名 -->
            <div
                v-if="secondPlace"
                class="rank-item rank-2">
                <div class="avatar-container">
                    <img
                        :src="secondPlace.avatar"
                        class="avatar" />
                </div>
                <div class="nickname">{{ secondPlace.nickname }}</div>
                <div class="value">
                    <img
                        class="mr-2 h-16 w-16"
                        src="@/assets/img/<EMAIL>"
                        alt="">{{ formatValue(secondPlace.value) }}
                </div>
            </div>

            <!-- 第一名 -->
            <div
                v-if="firstPlace"
                class="rank-item rank-1">
                <div class="avatar-container">
                    <img
                        :src="firstPlace.avatar"
                        class="avatar" />
                </div>
                <div class="nickname">{{ firstPlace.nickname }}</div>
                <div class="value">
                    <img
                        class="mr-2 h-16 w-16"
                        src="@/assets/img/<EMAIL>"
                        alt="">{{ formatValue(firstPlace.value) }}
                </div>
            </div>

            <!-- 第三名 -->
            <div
                v-if="thirdPlace"
                class="rank-item rank-3">
                <div class="avatar-container">
                    <img
                        :src="thirdPlace.avatar"
                        class="avatar" />
                </div>
                <div class="nickname">{{ thirdPlace.nickname }}</div>
                <div class="value">
                    <img
                        class="mr-2 h-16 w-16"
                        src="@/assets/img/<EMAIL>"
                        alt="">{{ formatValue(thirdPlace.value) }}
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { formatNumber } from '@/utils';

const props = defineProps({
    list: {
        type: Array,
        default: () => [],
    },
    type: {
        type: Number,
        required: true,
    },
});

const firstPlace = computed(() => props.list[0] || null);
const secondPlace = computed(() => props.list[1] || null);
const thirdPlace = computed(() => props.list[2] || null);

function formatValue(value) {
    return formatNumber(value || 0);
}
</script>

<style lang="less" scoped>
.tab4-top-three {
    .podium {
        display: flex;
        justify-content: center;
        align-items: flex-end;
        padding: 20px 0;

        .rank-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;

            .rank-badge {
                width: 24px;
                height: 24px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                font-weight: bold;
                color: #fff;
                margin-bottom: 8px;
            }

            .crown {
                font-size: 20px;
                margin-bottom: 5px;
            }

            .avatar-container {
                position: relative;

                .avatar {
                    border-radius: 50%;
                    border: 2px solid #fff;
                }
            }

            .nickname {
                font-size: 14px;
                line-height: 14px;
                margin-top: 4px;
                color: #fff;
                max-width: 80px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                text-align: center;
            }

            .value {
                margin-top: 3px;
                font-size: 11px;
                color: #ffffff;
                text-align: center;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            // 第一名样式
            &.rank-1 {
                background-image: url('@/assets/img/<EMAIL>');
                background-size: 100%;
                width: 139.5px;
                height: 192px;
                padding-top: 20px;

                .avatar {
                    width: 70px;
                    height: 70px;
                    border-color: #ffd700;
                }

                .nickname {
                    font-size: 14px;
                }

                .value {
                    font-size: 12px;
                    margin-top: 8px;
                }
            }

            // 第二名样式
            &.rank-2 {
                background-image: url('@/assets/img/<EMAIL>');
                background-size: 100%;
                width: 111.5px;
                height: 153.5px;
                padding-top: 15px;

                .avatar {
                    width: 56px;
                    height: 56px;
                    border-color: #c0c0c0;
                }
            }

            // 第三名样式
            &.rank-3 {
                background-image: url('@/assets/img/<EMAIL>');
                background-size: 100%;
                width: 111.5px;
                height: 153.5px;
                padding-top: 15px;
                .avatar {
                    width: 56px;
                    height: 56px;
                    border-color: #cd7f32;
                }
            }
        }
    }
}
</style>
