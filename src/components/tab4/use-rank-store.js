// src/components/tab4/use-rank-store.js
import { defineStore } from 'pinia';
import { giftRank } from '@/api';
import { to } from '@/auto-imports/utils';

export const RANK_TYPE = {
    SEND: 1, // 送礼榜
    RECEIVE: 2, // 收礼榜
};

export const RANK_TYPE_TEXT = {
    [RANK_TYPE.SEND]: '送礼榜',
    [RANK_TYPE.RECEIVE]: '收礼榜',
};

const useRankStore = defineStore('rank', () => {
    const list = ref([]);
    const selfRank = ref(null);
    const currentType = ref(RANK_TYPE.SEND);
    const loading = ref({ list: false, more: false });
    const error = ref({ hasError: false, message: '', canRetry: false });

    let page = 1;
    const SIZE = 20;
    let hasMore = true;
    let total = 0;

    // 标准化用户数据格式
    function normalizeUserData(user) {
        return {
            uid: user.uid || user.userInfo?.uid,
            username: user.username || user.userInfo?.username,
            nickname: user.nickname || user.userInfo?.nickname,
            value: user.value || user.amount || user.score || 0,
            rank: user.rankHuman || user.rank || user.ranking || 0,
        };
    }

    // 重置分页状态
    function resetPagination() {
        page = 1;
        hasMore = true;
        total = 0;
        list.value = [];
    }

    // 重置错误状态
    function resetError() {
        error.value = { hasError: false, message: '', canRetry: false };
    }

    // 设置错误状态
    function setError(message, canRetry = true) {
        error.value = { hasError: true, message, canRetry };
    }

    // 获取榜单数据
    async function queryRankList(isLoadMore = false) {
        if (!hasMore && isLoadMore)
            return;

        const loadingKey = isLoadMore ? 'more' : 'list';
        if (loading.value[loadingKey])
            return;

        // 重置错误状态
        if (!isLoadMore) {
            resetError();
        }

        loading.value[loadingKey] = true;

        try {
            const [res] = await giftRank({
                type: currentType.value,
                page,
                size: SIZE,
            });

            if (res?.code === 0) {
                console.log(2132131);

                const { data } = res;
                const rawList = data.list || data.ranks || [];
                const newList = rawList.map(normalizeUserData);

                if (isLoadMore) {
                    list.value = [...list.value, ...newList];
                }
                console.log(12312312);
                list.value = newList;
                // 处理自己的排名数据
                if (data.self || data.mine || data.my) {
                    const selfData = data.self || data.mine || data.my;
                    selfRank.value = normalizeUserData(selfData);
                    console.log(1111);
                }
                else {
                    selfRank.value = null;
                }

                total = data.total || data.totalCount || 0;
                hasMore = list.value.length < total && newList.length === SIZE;

                if (newList.length > 0) {
                    page += 1;
                }

                // 成功后重置错误状态
                resetError();
            }
        }
        catch (error) {
            console.error('获取榜单数据异常：', error);
            const errorMsg = '网络错误，请检查网络连接';

            if (!isLoadMore) {
                setError(errorMsg, true);
                showToast(errorMsg);
            }
        }
        finally {
            loading.value[loadingKey] = false;
        }
    }

    // 重试获取数据
    async function retryQuery() {
        resetPagination();
        await queryRankList();
    }

    // 切换榜单类型
    async function switchRankType(type) {
        if (currentType.value === type)
            return;

        currentType.value = type;
        resetPagination();
        resetError();
        await queryRankList();
    }

    // 加载更多
    async function loadMore() {
        await queryRankList(true);
    }

    // 获取前三名数据
    const topThreeList = computed(() => list.value.slice(0, 3));

    // 获取第4名及以后的数据
    const normalList = computed(() => list.value.slice(3));

    // 是否有自己的排名
    const hasSelfRank = computed(() => !!selfRank.value);

    // 是否显示空状态
    const showEmptyState = computed(() =>
        !loading.value.list && !error.value.hasError && list.value.length === 0,
    );

    return {
        list,
        selfRank,
        currentType,
        loading,
        error,
        hasMore,
        topThreeList,
        normalList,
        hasSelfRank,
        showEmptyState,
        queryRankList,
        retryQuery,
        switchRankType,
        loadMore,
        RANK_TYPE,
        RANK_TYPE_TEXT,
    };
});

export default useRankStore;
