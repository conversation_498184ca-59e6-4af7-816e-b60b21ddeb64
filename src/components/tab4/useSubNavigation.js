// 子导航 hook
import { computed, ref } from 'vue';
import { SUB_NAV_CONFIG } from './constants';

export function useSubNavigation(initialValue = 1) {
    const subNav = ref(initialValue);

    // 是否为低级（人工温泉）
    const isLow = computed(() => subNav.value === 1);

    // 当前导航配置
    const currentNavConfig = computed(() =>
        SUB_NAV_CONFIG.find(item => item.value === subNav.value),
    );

    // 切换导航
    const changeSubNav = async (value, callback) => {
        if (subNav.value === value)
            return;

        subNav.value = value;
        await nextTick();

        if (callback && typeof callback === 'function') {
            await callback(value);
        }
    };

    return {
        subNav,
        isLow,
        currentNavConfig,
        changeSubNav,
        SUB_NAV_CONFIG,
    };
}
