<template>
    <div
        class="common-modal-reward"
        :class="[size, color]">
        <span class="absolute left-[2px] top-[0] z-200 text-[10px] text-[#fff] font-800">{{ reward?.resource_id }}</span>
        <div class="reward-image">
            <img :src="reward.url">
            <div
                v-if="showTag && size === 'small' && reward.battlePassType === BATTLE_PASS_TYPE.SUPER"
                class="reward-super-tag"></div>
            <div
                v-if="reward.num"
                class="reward-count">
                {{ reward.num }}个
            </div>
            <div
                v-else-if="reward.time"
                class="reward-count">
                {{ reward.time }}天
            </div>
        </div>
        <div class="reward-text">
            <div
                v-if="showTotalPrice && reward.num && reward.originPrice"
                class="reward-mark">
                价值{{ omitValue(reward.originPrice * reward.num) }}豆
            </div>
            <div
                v-else
                class="reward-mark">
                {{ reward.mark }}
            </div>
            <div class="reward-name">{{ reward.name }}</div>
        </div>
    </div>
</template>

<script setup>
import { BATTLE_PASS_TYPE } from '@/stores/modules/use-battle-pass-store';

defineProps({
    size: {
        type: String,
        default: 'large',
    },
    color: {
        type: String,
        default: 'deep-blue',
    },
    reward: {
        type: Object,
        required: true,
    },
    showTag: {
        type: Boolean,
        default: false,
    },
    showTotalPrice: {
        type: Boolean,
        default: false,
    },
});
</script>

<style lang="less" scoped>
.common-modal-reward {
    position: relative;
    &.large {
        width: 112.5px;
        margin-bottom: 43px;
        &.deep-blue {
            .reward-image {
                .pic-bg(url('images/<EMAIL>'), 112.5px, 112.5px);
            }
        }
        &.light-blue {
            .reward-image {
                .pic-bg(url('images/<EMAIL>'), 112.5px, 112.5px);
            }
        }
        &.light-pink {
            .reward-image {
                .pic-bg(url('images/<EMAIL>'), 112.5px, 112.5px);
            }
        }
        .reward-text {
            top: 117px;
            font-size: 13px;
            line-height: 18px;
        }
        .reward-count {
            right: 6px;
            bottom: 3px;
            font-size: 18px;
            line-height: 20px;
        }
    }
    &.small {
        margin-bottom: 43px;
        width: 60.75px;
        &.deep-blue {
            .reward-image {
                .pic-bg(url('images/<EMAIL>'), 60.75px, 60.75px);
            }
        }
        &.light-blue {
            .reward-image {
                .pic-bg(url('images/<EMAIL>'), 60.75px, 60.75px);
            }
        }
        &.light-pink {
            .reward-image {
                .pic-bg(url('images/<EMAIL>'), 60.75px, 60.75px);
            }
        }
        .reward-image {
            .reward-super-tag {
                .pic-bg(url('images/<EMAIL>'), 28px, 18px);
                position: absolute;
                right: -6.85px;
                top: -1.63px;
            }
        }
        .reward-text {
            top: 65px;
            font-size: 12px;
            line-height: 15px;
        }
        .reward-count {
            right: 3px;
            bottom: 3px;
            font-size: 12px;
            line-height: 15px;
        }
    }
    .reward-image {
        .flex-center();
        position: relative;
        img {
            max-width: 80%;
            max-height: 80%;
        }
        .reward-count {
            .text-fill-around(#fff, #ad5a00, 1px);
            position: absolute;
            font-size: 12px;
            font-family:
                Douyin Sans,
                Douyin Sans-Bold;
            font-weight: 700;
            text-align: center;
            color: #ffffff;
            letter-spacing: -0.24px;
        }
    }
    .reward-text {
        .x-center();
        width: 110%;
        font-family:
            Alibaba PuHuiTi 2,
            Alibaba PuHuiTi 2-55 Regular;
        font-weight: normal;
        text-align: center;
        color: #fff99c;
        letter-spacing: -0.24px;
        > div {
            .one-line();
        }
    }
}
</style>
