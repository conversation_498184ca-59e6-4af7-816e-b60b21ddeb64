<template>
    <div class="home-global-level">
        <tab-1-level-num
            :level="levelStore.currentLevel"
            :locked="false" />
        <div class="progress">
            <div class="top">
                <!-- 没到60级 -->
                <div
                    v-if="!levelStore.isReachMaxLevel"
                    class="value">
                    {{ expProgress }}/{{ levelStore.everyLevelExp }}
                </div>
                <div
                    v-if="levelStore.canBuyLevelCount"
                    class="icon-buy-level"
                    @click="openBuyLevelModal"></div>
                <!-- 到60级没买豪华通行证 -->
                <div
                    v-if="levelStore.isReachMaxLevel && battlePassStore.hasBuySuperBattlePass"
                    class="value">
                    开启榜单争夺
                </div>
                <!-- 到60级买了豪华通行证 -->
                <div
                    v-if="levelStore.isReachMaxLevel && !battlePassStore.hasBuySuperBattlePass"
                    class="value">
                    豪华进阶可开启榜单争夺
                </div>
            </div>
            <!-- 进度条 -->
            <div class="bottom">
                <div class="progress-bar">
                    <div
                        class="progress-content"
                        :style="{ width: expProgessPercent }"></div>
                </div>
            </div>
        </div>
        <!-- 没到60级，去TAB2 -->
        <div
            v-if="!levelStore.isReachMaxLevel"
            class="btn-upgrade"
            @click="toUpgrade"></div>
        <!-- 到60级没买豪华通行证，去通行证弹窗 -->
        <div
            v-if="levelStore.isReachMaxLevel && !battlePassStore.hasBuySuperBattlePass"
            class="btn-buy-battle-pass"
            @click="toBuyBattlePass"></div>
        <!-- 到60级买了豪华通行证，去TAB4 -->
        <div
            v-if="levelStore.isReachMaxLevel && battlePassStore.hasBuySuperBattlePass"
            class="btn-see-rank"
            @click="toSeeRank"></div>
    </div>
</template>

<script setup>
import useLevelStore from '@/stores/modules/use-level-store';
import useBattlePassStore from '@/stores/modules/use-battle-pass-store';
import useTabStore, { TABS } from '@/stores/modules/use-tab-store';

const levelStore = useLevelStore();
const battlePassStore = useBattlePassStore();
const tabStore = useTabStore();
const expProgress = computed(() => Number.parseInt(levelStore.currentExp % levelStore.everyLevelExp));
const expProgessPercent = computed(() =>
    levelStore.isReachMaxLevel
        ? '100%'
        : `${expProgress.value / levelStore.everyLevelExp * 100}%`,
);

function toUpgrade() {
    tabStore.setTabId(TABS.TAB2);
}

function toBuyBattlePass() {
    useEventBus('modal-buy-battle-pass').emit({ show: true });
}

function toSeeRank() {
    tabStore.setTabId(TABS.TAB4);
}

function openBuyLevelModal() {
    useEventBus('modal-buy-level').emit({ show: true });
}
</script>

<style lang="less" scoped>
.home-global-level {
    .pic-bg(url('images/<EMAIL>'), 375px, 63.5px);
    padding-left: 5.5px;
    display: flex;
    align-items: center;
    .progress {
        margin-left: 10px;
        .top {
            .flex-space-between();
            width: 171px;
            padding-left: 3.5px;
            .value {
                font-size: 15px;
                font-family:
                    Alibaba PuHuiTi 2,
                    Alibaba PuHuiTi 2-65 Medium;
                font-weight: normal;
                text-align: center;
                color: #fffde5;
                line-height: 15px;
                letter-spacing: -0.3px;
            }
            .icon-buy-level {
                .pic-bg(url('images/<EMAIL>'), 69.5px, 18.5px);
            }
        }
        .bottom {
            .flex-center();
            margin-top: 6px;
            width: 171px;
            height: 14px;
            .progress-bar {
                position: relative;
                width: 100%;
                height: 100%;
                background: #061948;
                border-radius: 7px;
                overflow: hidden;
                .progress-content {
                    .y-center();
                    left: 0;
                    height: 100%;
                    background: linear-gradient(0deg, rgba(255, 204, 29, 1) 0%, #fff836 100%), #fff261;
                    border-radius: 7px;
                }
            }
        }
    }
    .btn-upgrade {
        .pic-bg(url('images/<EMAIL>'), 123px, 38px);
        margin-left: 17.5px;
    }
    .btn-buy-battle-pass {
        .pic-bg(url('images/<EMAIL>'), 123px, 38px);
        margin-left: 17.5px;
    }
    .btn-see-rank {
        .pic-bg(url('images/<EMAIL>'), 123px, 38px);
        margin-left: 17.5px;
    }
}
</style>
