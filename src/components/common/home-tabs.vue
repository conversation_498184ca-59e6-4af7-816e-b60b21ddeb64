<template>
    <div class="home-tabs">
        <div
            v-for="tab in tabStore.tabs"
            :key="tab.id"
            class="tab"
            :class="[`tab-${tab.id}`, { active: tabStore.currentTabId === tab.id }]"
            @click.stop="() => setTab(tab.id)">
            {{ tab.text }}
        </div>
    </div>
</template>

<script setup>
import useTabStore from '@/stores/modules/use-tab-store';

const tabStore = useTabStore();

async function setTab(id) {
    tabStore.setTabId(id);
}
</script>

<style lang="less" scoped>
.home-tabs {
    .pic-bg(url('images/<EMAIL>'), 365px, 32px);
    .flex-space-between();
    .tab {
        .flex-center();
        width: 90px;
        height: 36px;
        font-size: 18px;
        font-family: HYFengShangHei, HYFengShangHei-85J;
        font-weight: normal;
        text-align: center;
        color: #ebebeb;
        line-height: 15px;
        letter-spacing: -0.36px;
        &.active {
            color: #e16501;
        }
    }
}
</style>
