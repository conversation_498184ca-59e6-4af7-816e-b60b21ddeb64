<template>
    <div
        class="home-top-bar"
        :class="{ special: tabStore.isHalfScreen }">
        <div
            v-if="tabStore.isHalfScreen"
            class="title">
            海洋之约 TT通行证
        </div>
        <div
            class="icon-back"
            @click="ruleStore.back"></div>
        <div
            class="icon-rule"
            @click="ruleStore.linkToRule"></div>
    </div>
</template>

<script setup>
import useRuleStore from '@/stores/modules/use-rule-store';
import useTabStore from '@/stores/modules/use-tab-store';

const ruleStore = useRuleStore();
const tabStore = useTabStore();
</script>

<style lang="less" scoped>
.home-top-bar {
    .flex-space-between();
    width: 100%;
    position: absolute;
    top: 46.5px;
    padding: 0 6.5px 0 11px;
    &.special {
        top: 11.5px;
    }
    .title {
        .x-text-center();
        font-size: 28px;
        font-family:
            <PERSON><PERSON><PERSON>,
            <PERSON><PERSON><PERSON> Sans-Bold;
        font-weight: 700;
        text-align: center;
        color: #ffffff;
        line-height: 15px;
        letter-spacing: -0.56px;
    }
    .icon-back {
        .pic-bg(url('images/<EMAIL>'), 21px, 21px);
    }
    .icon-rule {
        .pic-bg(url('images/<EMAIL>'), 36px, 20px);
    }
}
</style>
