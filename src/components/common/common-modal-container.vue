<template>
    <modal-container
        :show="show"
        :close-on-click-overlay="true"
        @update:show="updateShow">
        <div
            class="common-modal-container"
            :style="bgStyle">
            <div
                class="icon-close"
                @click="close"></div>
            <div class="title">{{ title }}</div>
            <div class="content">
                <slot />
            </div>
        </div>
    </modal-container>
</template>

<script setup>
import { convertVw } from '@/utils/others';

const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    title: {
        type: String,
        default: '',
    },
    bg: {
        type: Object,
        required: true,
    },
});
const emits = defineEmits('update:show');

const bgStyle = computed(() => {
    return {
        width: convertVw(props.bg.width),
        height: convertVw(props.bg.height),
        background: `url(${props.bg.url}) no-repeat`,
        backgroundSize: '100% 100%',
    };
});

function updateShow(status) {
    emits('update:show', status);
}

function close() {
    updateShow(false);
}
</script>

<style lang="less" scoped>
.common-modal-container {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .icon-close {
        .pic-bg(url('images/<EMAIL>'), 32px, 32px);
        position: absolute;
        right: 42px;
        top: -42px;
    }
    .title {
        .flex-center();
        height: 43px;
        font-size: 21px;
        font-family: HYFengShangHei, HYFengShangHei-85J;
        font-weight: normal;
        text-align: center;
        color: #ffffff;
        line-height: 15px;
        letter-spacing: -0.42px;
    }
    .content {
        position: relative;
        flex: 1;
        overflow: scroll;
    }
}
</style>
