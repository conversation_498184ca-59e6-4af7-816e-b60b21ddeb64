import VueRouter from 'unplugin-vue-router/vite';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import Components from 'unplugin-vue-components/vite';
import AutoImport from 'unplugin-auto-import/vite';
import { VantResolver } from '@vant/auto-import-resolver';
import { VueRouterAutoImports } from 'unplugin-vue-router';
import legacy from '@vitejs/plugin-legacy';
import UnoCSS from 'unocss/vite';
import VueDevTools from 'vite-plugin-vue-devtools';
import vueSetupExtend from 'vite-plugin-vue-setup-extend';
import checker from 'vite-plugin-checker';
import { ViteImageOptimizer } from 'vite-plugin-image-optimizer';
import { visualizer } from 'rollup-plugin-visualizer';
import Inspect from 'vite-plugin-inspect';
import { mfv4UrlConfig } from './url-config';
import { unocssScopedFix } from './unocss-scoped-fix';

export const createVitePlugins = () => {
    return [
        // https://github.com/posva/unplugin-vue-router
        VueRouter({
            extensions: ['.vue'],
            routesFolder: 'src/pages',
            exclude: ['**/components/**/*'],
            dts: 'src/typed-router.d.ts',
            importMode: 'sync',
        }),

        vue({
            template: {
                transformAssetUrls: {
                    video: ['src', 'poster'],
                    source: ['src'],
                    img: ['src'],
                    image: ['xlink:href', 'href'],
                    use: ['xlink:href', 'href'],
                    'van-image': ['src'],
                },
            },
        }),
        vueJsx(),

        // https://github.com/antfu/unplugin-vue-components
        Components({
            dirs: ['src/components', 'src/pendant/components'],
            resolvers: [VantResolver()],
            dts: 'src/components.d.ts',
        }),

        // https://github.com/antfu/unplugin-auto-import
        AutoImport({
            include: [/\.[tj]sx?$/, /\.vue$/, /\.vue\?vue/],
            imports: [
                'vue',
                '@vueuse/core',
                VueRouterAutoImports,
                {
                    // add any other imports you were relying on
                    // 自定义增加全局自动导入的hooks
                    'vue-router/auto': ['useLink'],
                    pinia: ['storeToRefs'],
                },
            ],
            resolvers: [VantResolver()],
            // auto import for vue template
            vueTemplate: true,
            dts: 'src/auto-imports.d.ts',
            // Auto import for module exports under directories
            // 对所配目录下你导出的hooks进行自动导入，替代mixin
            dirs: ['src/auto-imports/**'],
            eslintrc: {
                enabled: true,
                filepath: './.eslintrc-auto-import.json', // Default `./.eslintrc-auto-import.json`
                globalsPropValue: true, // Default `true`, (true | false | 'readonly' | 'readable' | 'writable' | 'writeable')
            },
        }),

        // https://github.com/vitejs/vite/tree/main/packages/plugin-legacy#readme
        // 浏览器版本普及占比可在 https://browsersl.ist/ 查询
        legacy({
            /* 兼容历史浏览器 */
            // targets: 'last 2 versions and not dead, > 0.3%, Firefox ESR',
            additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
            /* 按 targets 版本输出针对历史浏览器的polyfills */
            // polyfills: true,
            /* 现代浏览器判断标准 */
            // modernTargets: 'edge>=79, firefox>=67, chrome>=64, safari>=12, chromeAndroid>=64, iOS>=12',
            /* 按 modernTargets 版本输出针对现代浏览器的polyfills */
            modernPolyfills: true,
        }),

        // https://github.com/antfu/unocss
        // see uno.config.ts for config
        UnoCSS({
            mode: 'vue-scoped',
        }),

        // https://github.com/vuejs/devtools-next
        ...process.env.NODE_ENV === 'development' ? [VueDevTools()]: [],

        // https://github.com/vbenjs/vite-plugin-vue-setup-extend
        // 允许 setup 语法糖上添加组件名属性
        vueSetupExtend(),

        // https://github.com/fi3ework/vite-plugin-checker
        {
            ...checker({
                eslint: {
                    lintCommand: 'eslint "."',
                    useFlatConfig: true,
                    dev: {
                        logLevel: ['error'],
                    },
                },
            }),
            apply: 'serve',
        },

        // https://github.com/FatehAK/vite-plugin-image-optimizer
        ViteImageOptimizer({
            // 排除文件名包含no_compress的图片
            exclude: /no_compress/,
            // 排除public文件夹下的图片
            includePublic: false,
            // 缓存压缩过的图片
            cache: true,
            cacheLocation: './imgCompression',
        }),

        // 活动全链接平台地址生成
        mfv4UrlConfig(),

        // 修复 unocss 'vue-scoped' mode下的热更新报错问题
        unocssScopedFix(),

        // https://github.com/btd/rollup-plugin-visualizer
        // 产物占用大小分析
        visualizer(),

        // https://github.com/antfu-collective/vite-plugin-inspect
        Inspect(),
    ];
};
