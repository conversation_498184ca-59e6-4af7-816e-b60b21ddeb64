const MOCK_ARRAY_LENGTH = 10; // 模拟数组长度
const fs = require('node:fs');

let file = null;

function log(...rest) {
    console.log(...rest);
}

// 对象key转换成数组
function objectKeys2Array(obj) {
    return obj instanceof Object ? Object.keys(obj) : [];
}

// 是否为数组
function isArray(arr) {
    return Array.isArray(arr);
}

try {
    file = fs.readFileSync('./activity.json', 'utf-8');
}
catch (err) {
    log('activity.json 不存在');
}

const content = JSON.parse(file).nested.activity.nested;
const { methods } = content[objectKeys2Array(content)[0]]; // 所有方法
/**
 * 模拟数据
 */ 
let mock = 'const API = {';
const mockEnd = `\n};
const getMockData = (type, payload) =>
    new Promise((resolve) => {
        const delay = Math.round(Math.random() * 10) * 50; // 模拟访问延迟
        let data;
        if (typeof API[type] === 'function')
            data = API[type](payload);
        else data = API[type];

        // eslint-disable-next-line no-console
        console.log(
            \`模拟接口请求名称<=== \${type} delay: \${delay} ms; payload: \${JSON.stringify(payload)}\`,
        );
        window.setTimeout(() => {
            // eslint-disable-next-line no-console
            console.log('模拟接口请求返回===>', data);
            resolve({
                code: 0,
                data,
                msg: '',
            });
        }, delay);
    });
export default getMockData;`;
// 数据类型
const TYPE = {
    STRING: ['string'],
    NUMBER: ['int32', 'int64', 'uint32', 'uint64', 'double'],
    BOOLEAN: ['bool'],
    USER_ITEM: ['userItem'],
};
// 常规类型
const BASE_TYPE_LIST = [...TYPE.STRING, ...TYPE.NUMBER, ...TYPE.BOOLEAN];
const MOCK_DATA = {
    STRING: () => 'Mock-刚好五个字',
    NUMBER: () => Math.ceil(Math.random() * 2),
    BOOLEAN: () => Math.random() > 0.5,
    USER_ITEM() {
        return {
            uid: 2416206,
            sex: 0,
            roomId: 2041761,
            channelId: 2048111,
            ttid: *********,
            account: 'tt*********',
            nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
        };
    },
};
console.time('used time');

/**
 * 请求方法
 */
const actionPrefixActivity = `
// 执行pb.cjs命令后会覆盖掉手写的代码，请谨慎操作!
import request from '../utils/request';
import getMockData from './mockData';

export const fetchApi = ({
    proPrefix = '/activity.Activity/',
    api,
    data = {},
    config = {},
}) => {
    const { mock } = myWebview.params;
    if (mock)
        return to(getMockData(api, data));

    const url = \`\${proPrefix}/\${api}\`.replace('//', '/');
    return to(request.post(url, data, config));
};

/**
 * ********************************
 * *********  活动接口 *************
 * ********************************
 */`;

const actionPrefixPendant = `
// 执行pb-p.cjs命令后会覆盖掉手写的代码，请谨慎操作!
import request from './request';
import getMockData from './mockData';

export const fetchApi = ({
    proPrefix = '/room.Room/',
    api,
    data = {},
    config = {},
}) => {
    const { mock } = myWebview.params;
    if (mock)
        return to(getMockData(api, data));

    const url = \`\${proPrefix}/\${api}\`.replace('//', '/');
    return to(request.post(url, data, config));
};

/**
 * ********************************
 * *********  挂件接口 *************
 * ********************************
 */`;

// 获取请求参数
// function getMethodReqParams(requestType) {
//     return objectKeys2Array((content[requestType] || {}).fields);
// }

// function getMethodRspParams(responseType) {
//     if (responseType === 'types.EmptyRes')
//         return [];
//     return objectKeys2Array((content[responseType] || {}).fields);
// }

// 判断是否有数组类型
function handleRepeated(contentKey, Obj) {
    objectKeys2Array(Obj).forEach((key) => {
        if (!isArray(Obj[key].tmpListData))
            return;
        Obj[key].tmpListData.forEach((item, index) => {
            const tmp = item instanceof Object ? { ...Obj[key] } : item;
            delete tmp.tmpListData;

            Obj[key].tmpListData[index] = tmp;
        });
        handleRepeated(key, Obj[key]);
    });
}

function handleObject2Array(contentKey, Obj) {
    function setList2Obj(item) {
        objectKeys2Array(item).forEach((key) => {
            if (!item[key].tmpListData)
                return;

            item[key] = item[key].tmpListData;
            handleObject2Array(key, item[key]);
        });
    }

    isArray(Obj)
        ? Obj.forEach((item) => {
            setList2Obj(item);
        })
        : setList2Obj(Obj);
}

function main(apiType = 'activity') {
    const actionPrefix
        = apiType === 'activity' ? actionPrefixActivity : actionPrefixPendant;
    let REQUEST_API_MAP = 'const REQUEST_API_MAP = {';
    let API = 'export default {';
    let interfaceRequest = '';
    let apiUrl = '';
    let mockUrl = '';

    // 生成模拟数据
    function getFields(contentKey, Obj, requestObj) {
        let contentObj = {};
        try {
            contentObj
                = content[contentKey]
                || content[requestObj.responseType].nested[contentKey]
                || {};
        }
        catch (err) {
            log('getFields contentObj error', err);
        }
        objectKeys2Array(contentObj.fields).forEach((key) => {
            const contentKeyType = contentObj.fields[key].type; // 当前属性对应的数据类型
            let mockValue = {};
            // 是否为数组
            if (isArray(Obj[key])) {
                // log('isArray');
            }
            else if (contentObj.fields[key].rule === 'repeated') {
                mockValue.tmpListData = Array.from({
                    length: MOCK_ARRAY_LENGTH,
                }).fill({});
            }
            // 判断类型
            if (TYPE.STRING.includes(contentKeyType)) {
                mockValue = `Mock-${key}`;
            }
            if (TYPE.BOOLEAN.includes(contentKeyType)) {
                mockValue = MOCK_DATA.BOOLEAN();
            }
            if (TYPE.NUMBER.includes(contentKeyType)) {
                mockValue = MOCK_DATA.NUMBER();
            }
            // 判断key的属性
            if (key === 'nickname') {
                mockValue = MOCK_DATA.USER_ITEM().nickname;
            }
            else if (key === 'uid') {
                mockValue = MOCK_DATA.USER_ITEM().uid;
            }
            else if (['account', 'username'].includes(key)) {
                mockValue = MOCK_DATA.USER_ITEM().account;
            }
            else if (['roomId', 'channelId'].includes(key)) {
                mockValue = MOCK_DATA.USER_ITEM().channelId;
            }
            else if (
                key.includes('time')
                || key.includes('stamp')
                || key.includes('Time')
            ) {
                mockValue = Math.ceil(new Date().getTime() / 1000);
            }
            if (BASE_TYPE_LIST.includes(contentKeyType)) {
                // log('BASE_TYPE_LIST');
            }
            else if (
                TYPE.USER_ITEM.includes(contentKeyType)
                && contentObj.fields[key].rule !== 'repeated'
            ) {
                mockValue = MOCK_DATA.USER_ITEM();
            }
            else {
                let nestedValue = {};
                let nestedValues = null;
                if ((content[contentKeyType] || {}).values) {
                    nestedValues = (content[contentKeyType] || {}).values;
                }
                else if (contentObj.nested) {
                    nestedValues = (contentObj.nested[contentKeyType] || {})
                        .values;
                }
                if (nestedValues) {
                    nestedValue
                        = Object.values(nestedValues)[
                            Math.floor(
                                Math.random()
                                * Object.values(nestedValues).length,
                            )
                        ];
                }
                if (contentObj.fields[key].rule === 'repeated') {
                    mockValue.tmpListData = Array.from({
                        length: MOCK_ARRAY_LENGTH,
                    }).fill(nestedValue);
                }
                else {
                    mockValue = nestedValue;
                }

                Obj[key] = mockValue;
                getFields(contentKeyType, Obj[key], requestObj);
            }

            Obj[key] = mockValue;
        });
    }

    objectKeys2Array(methods).forEach((key) => {
        const requestObj = methods[key];
        const Obj = {};
        const responseTypeName = requestObj.responseType;
        // const reqParams = getMethodReqParams(requestObj.requestType);
        // const rspParams = getMethodRspParams(responseTypeName);
        REQUEST_API_MAP += `\n  ${key}: '${key}',`;
        API += `\n  ${key},`;
        interfaceRequest += `/** @type {function(import('./api.d.ts').${requestObj.requestType}):Promise<[{code:number,msg:string,data:import('./api.d.ts').${responseTypeName}},any]>} */
        export const ${key} = (data, config) =>fetchApi({ api: REQUEST_API_MAP.${key}, data, config });\n\n`;
        // 生成模拟数据
        getFields(responseTypeName, Obj, requestObj);
        handleRepeated(responseTypeName, Obj);
        handleObject2Array(responseTypeName, Obj);
        mock += `\n    ${key}() {
            return ${JSON.stringify(Obj)}
        },`;
    });

    REQUEST_API_MAP += '\n};';
    API += '\n};';
    mock += mockEnd;
    // 写入本地文件
    if (apiType === 'activity') {
        apiUrl = 'src/api/index.js';
        mockUrl = 'src/api/mockData.js';
    }
    else {
        apiUrl = 'src/pendant/api/index.js';
        mockUrl = 'src/pendant/api/mockData.js';
    }
    fs.writeFileSync(
        apiUrl,
        `${actionPrefix}\n${REQUEST_API_MAP}\n\n${interfaceRequest}\n\n${API}\n`,
        { flags: 'w' },
    );
    console.log('build apis.js success!');
    fs.writeFileSync(mockUrl, mock, { flags: 'w' });
    console.log('build mockData.js success!');
    console.timeEnd('used time');
}

module.exports = main;
