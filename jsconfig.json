{"compilerOptions": {"module": "esnext", "jsx": "preserve", "lib": ["esnext", "dom", "dom.iterable", "scripthost"], "target": "esnext", "baseUrl": "./", "paths": {"@/*": ["src/*"]}, "types": ["node", "unplugin-vue-router/client"], "moduleResolution": "<PERSON><PERSON><PERSON>", "allowSyntheticDefaultImports": true}, "exclude": ["node_modules", "dist"], "include": ["src/**/*.vue", "src/**/*.js", "src/**/*.jsx", "src/components.d.ts", "src/auto-imports.d.ts", "src/typed-router.d.ts"], "vueCompilerOptions": {"globalTypesPath": "./vue-global-types.d.ts"}}