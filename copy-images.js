import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
import { rewardMap } from './src/config/reward.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const sourceFilePath = path.resolve(__dirname, './src/assets/img/node/NODE.png');
const destinationFilePath = path.resolve(__dirname, './src/assets/img/rewards/NODE.png');
const destinationDirPath = path.resolve(__dirname, './src/assets/img/rewards/');

const images = Object.keys(rewardMap);

for (const image of images) {
    fs.copyFileSync(sourceFilePath, destinationFilePath);
    fs.renameSync(destinationFilePath, path.join(destinationDirPath, `${image}.png`));
    console.log('创建图片：', `src/assets/img/rewards/${image}.png`);
}
