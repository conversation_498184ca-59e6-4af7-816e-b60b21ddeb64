# Activity Mock Server 实现说明

## 概述

本文档详细说明了基于 `activity.proto` 文件生成的 Mock 服务器实现。

## 文件结构

```
server/
├── activity.proto          # Protocol Buffers 定义文件
├── server.js              # 主服务器实现
├── test-server.js         # 测试脚本
├── start.js               # 启动脚本
├── README.md              # 使用文档
└── IMPLEMENTATION.md      # 实现说明（本文件）
```

## 核心实现

### 1. 服务器架构

- **框架**: Express.js
- **端口**: 3002
- **协议**: HTTP REST API（模拟 gRPC 接口）
- **数据存储**: 内存存储（Map 结构）

### 2. 接口映射

Proto 文件中的 gRPC 服务方法映射为 HTTP POST 接口：

```
rpc init(InitReq) returns (InitResp)
↓
POST /activity.Activity/init
```

### 3. 数据模型

#### 用户数据结构
```javascript
{
    uid: number,                    // 用户ID
    exp: number,                    // 经验值
    hasBuyBattlePass: boolean,      // 是否购买通行证
    battlePassType: number,         // 通行证类型 1:普通 2:进阶 3:豪华
    hasBuyLevel: number,            // 已购买等级
    hasOpenBox: number,             // 是否开启宝箱 0:否 1:是
    shell: number,                  // 贝壳数量
    tbean: number,                  // T豆数量
    receiveRecords: Array,          // 领取记录
    lotteryRecords: Array           // 抽奖记录
}
```

#### 奖励数据结构
```javascript
{
    id: string,                     // 奖励ID
    num: number,                    // 数量
    time: number                    // 时间戳
}
```

### 4. 业务逻辑实现

#### 通行证系统
- **初始化**: 返回用户完整状态和系统配置
- **升级**: 更新用户通行证类型，返回额外奖励
- **领取奖励**: 单个等级奖励领取，记录领取状态
- **一键领取**: 批量领取当前等级内所有可领取奖励

#### 任务系统
- **任务进度**: 根据任务类型返回不同的任务列表
- **领取记录**: 返回用户所有等级的领取状态和奖励配置

#### 抽奖系统
- **抽奖数据**: 返回用户当前贝壳数量
- **执行抽奖**: 扣除贝壳，生成随机奖励，记录抽奖历史
- **抽奖记录**: 分页返回用户抽奖历史

#### 排行榜系统
- **实时榜单**: 生成模拟排行榜数据，支持分页
- **荣耀堂**: 返回历史期次的排行榜数据

#### 资产系统
- **T豆查询**: 返回用户当前T豆数量
- **宝箱开启**: 增加用户经验，支持通行证加成

#### 兑换购买
- **奖励兑换**: 扣除贝壳，兑换指定奖励
- **等级购买**: 消耗T豆，直接提升用户等级和经验

### 5. 参数验证

每个接口都有对应的参数验证规则：

```javascript
const validationRules = {
    InitReq: ['uid'],
    UpgradeBattlePassReq: ['uid', 'type'],
    ReceiveRewardReq: ['uid', 'level'],
    // ... 更多规则
};
```

### 6. 错误处理

- 参数验证失败返回 400 错误
- 业务逻辑错误（如余额不足）通过异常处理
- 服务器内部错误返回 500 错误

### 7. 响应格式

统一的响应格式：

```javascript
{
    code: 0,                        // 0:成功 其他:失败
    msg: "success",                 // 消息
    data: {                         // 具体数据，格式遵循 proto 定义
        // ...
    }
}
```

## 特性说明

### 1. 数据持久化
- 使用 Map 结构在内存中存储用户数据
- 支持多用户数据隔离
- 重启服务器会重置所有数据

### 2. 动态数据生成
- 用户首次访问时自动初始化数据
- 排行榜、任务进度等数据动态生成
- 支持随机化模拟真实场景

### 3. 业务逻辑模拟
- 抽奖消耗贝壳逻辑
- 通行证升级奖励逻辑
- 等级购买经验增长逻辑
- 宝箱开启经验获取逻辑

### 4. 开发友好
- 详细的日志输出
- 完整的参数验证
- 清晰的错误信息
- 支持 CORS 跨域请求

## 使用场景

### 1. 前端开发
- 无需等待后端接口开发完成
- 可以并行进行前端功能开发
- 支持完整的业务流程测试

### 2. 接口联调
- 验证前端请求参数格式
- 测试错误处理逻辑
- 确认响应数据结构

### 3. 演示展示
- 提供稳定的数据源
- 支持完整的功能演示
- 无需依赖外部服务

## 扩展说明

### 1. 添加新接口
1. 在 `mockResponses` 中添加响应生成函数
2. 在 `apiEndpoints` 中添加路由配置
3. 在 `validationRules` 中添加参数验证规则

### 2. 修改数据结构
1. 更新 `initUser` 函数中的用户初始化逻辑
2. 修改相关接口的响应数据格式
3. 确保与 proto 文件定义保持一致

### 3. 增强业务逻辑
1. 在对应的响应生成函数中添加业务逻辑
2. 考虑数据一致性和状态管理
3. 添加必要的错误处理

## 注意事项

1. **仅用于开发测试**: 不适用于生产环境
2. **数据不持久化**: 重启后数据会丢失
3. **简化业务逻辑**: 某些复杂逻辑进行了简化
4. **性能考虑**: 大量数据时可能影响性能
5. **安全性**: 没有实现认证和授权机制
