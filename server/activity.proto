
syntax = "proto3";

package activity;

service Activity {
  // 初始化
  rpc init(InitReq) returns (InitResp) {};
  // 升级通行证
  rpc upgradeBattlePass(UpgradeBattlePassReq) returns (UpgradeBattlePassResp) {};
  // 领取奖励
  rpc receiveReward(ReceiveRewardReq) returns (ReceiveRewardResp) {};
  // 一键领取
  rpc receiveAll(ReceiveAllRewardReq) returns (ReceiveAllRewardResp) {};
  // 通行证任务进度
  rpc battlePassTaskProcess(BattlePassProcessReq) returns (BattlePassProcessResp) {};
  // 通行证奖励领取进度
  rpc battlePassReceiveRecord(BattlePassReceiveRecordReq) returns (BattlePassReceiveRecordResp) {};
  // 抽奖数据
  rpc lotteryData(LotteryDataReq) returns (LotteryDataResp) {};
  // 抽奖
  rpc lottery(LotteryReq) returns (LotteryResp) {};
  // 抽奖记录
  rpc lotteryRecord(LotteryRecordReq) returns (LotteryRecordResp) {};
  // 获取用户t豆
  rpc getUserTbean(GetUserTbeanReq) returns (GetUserTbeanResp) {};
  // 移动端购买风控
  rpc buyRiskControl(BuyRiskControlReq) returns(RiskControlRes);
  // 开启宝箱获取经验值
  rpc openTreasureBox(OpenTreasureBoxReq) returns (OpenTreasureBoxResp) {};
  // 榜单
  rpc giftRank(GiftRankReq) returns (GiftRankResp) {};
  // 荣耀堂
  rpc gloryHall(GloryHallReq) returns (GloryHallResp) {};
  // 兑换奖励
  rpc exchangeReward(ExchangeRewardReq) returns (ExchangeRewardResp) {};
  // 购买等级
  rpc buyLevel(BuyLevelReq) returns (BuyLevelResp) {};
  // 获取可完成任务的房间集合
  rpc getCids(GetCidsReq) returns (GetCidsResp) {};
}

message GetCidsReq {
  uint32 uid = 1;
}

message GetCidsResp {
  repeated uint32 cids = 1;
}


message GiftRankResp {
  uint32 total = 1; // 总数量
  repeated CommonRankItem list = 2; // 榜单列表
  CommonRankItem self = 3; 
}

message GiftRankReq {
  uint32 uid = 1; // 用户uid
  uint32 page = 2; // 页码
  uint32 size = 3; // 每页数量
}

message GloryHallResp {
  message RankList {
    uint32 startTime = 1; // 开始时间
    uint32 endTime = 2; // 结束时间
    GiftRankResp rankDetail = 3;
  }
  repeated RankList rankList = 1;
}

message GloryHallReq {
  uint32 uid = 1; // 用户uid
  uint32 page = 2; // 页码
  uint32 size = 3; // 每页数量
}


message BuyRiskControlReq {
  uint32 uid = 1; // 当前操作用户uid
  string deviceId = 2; // 当前用户设备
  string ip = 3; // 客户端ip
  string clientType = 4; // 客户端类型 ANDROID:2;IOS:1; WEB:8;CAR:4; PC TT:5; TX_MINI: 7
  string marketId = 5; // 包体类型
  string appVersion = 6; // app版本
  string faceToken = 10; // 拉起人脸后回调此接口带上的token
  string pcToken = 11; // pc操作风控回调此接口带上的token
  uint32 appVersionCode = 12; // app版本号
  uint32 type = 13;  // 通行证类型 1:超值通行证 2:豪华通行证 3:升级
}

message RiskControlRes {
  int32 errCode = 1; // 透传code：0为成功，<0皆为失败
  string errMsg = 2; // 透传错误信息
  string errInfo = 3; // 透传额外的错误信息 Bytes as base64 encoded strings
}

message InitReq {
  uint32 uid = 1;
}

message InitResp {
  uint32 serverTime = 1; // 服务器时间
  uint32 startTime = 2; // 活动开始时间
  uint32 endTime = 3; // 活动结束时间
  bool hasBuyBattlePass = 4; // 是否购买通行证
  uint32 battlePassType = 5; // 通行证类型，1:普通通行证 2:进阶通行证 3:豪华通行证
  uint32 exp = 6;  // 当前用户经验
  uint32 passStartDate = 7;  // 通行证开始日期 eg： 20250428
  uint32 hasBuyLevel = 8;  // 用户已购买等级
  uint32 everyLevelPrize = 9; // 每等级花费：豆
  uint32 buyLevelLimit = 10; // 可购买等级上限
  uint32 everyLimitExp = 11; // 每等级经验
  uint32 openRisk = 12; // 1-开启风控 0-未开启风控
  uint32 advancedBattlePassPrice = 13; // 进阶通行证所需t豆
  uint32 superBattlePassPrice = 14; // 豪华通行证所需t豆
  uint32 hasOpenBox = 15; // 是否开启经验宝箱
  uint32 nextCanOpenTime = 16; // 下一次宝箱刷新时间
  uint32 openBoxExtraExp = 17; // 进阶后可以获取的额外经验值
  uint32 maxLevel = 18; // 通行证最高等级
}

message UpgradeBattlePassReq {
  uint32 uid = 1;
  uint32 type = 2; // 通行证类型，2:普通通行证 3:豪华通行证
  string token = 3;
}

message UpgradeBattlePassResp {
  uint32 getExtraExp = 1; // 额外获取的经验值
  repeated Reward rewards = 2; //额外奖励情况
  bool success = 3; // 是否购买成功
}

message BattlePassReceiveRecordReq {
  uint32 uid = 1;
}

message BattlePassReceiveRecordResp {
  message recordItem {
    uint32 level = 1; //等级
    uint32 type = 2;  //该等级目前已领取的通行证类型 1-普通 2-进阶 3-豪华
  }
  message LevelInfo {
    repeated Reward rewards = 1; // 当前等级配置奖励情况
  }

  message LevelRewardList {
    repeated LevelInfo levels = 1; // 所有等级的奖励，顺序对应等级
  }

  message AllRewards {
    repeated LevelInfo normalCard = 1;  // 普通通行证奖励配置
    repeated LevelInfo advancedCard = 2; // 进阶通行证配置
    repeated LevelInfo superCard = 3; // 豪华通行证配置
  }

  repeated recordItem records = 1; //每个等级领取情况
  AllRewards allRewards = 2; // 通行证配置情况
}


message ReceiveRewardReq {
  uint32 uid = 1;
  string token = 2;
  uint32 level = 3; //等级 1-60
}

message ReceiveRewardResp {
  repeated Reward rewards = 1; //奖励情况
}

message ReceiveAllRewardReq {
  uint32 uid = 1;
  string token = 2;
}

message ReceiveAllRewardResp {
  repeated Reward rewards = 1; //奖励情况
}

message BuyBattlePassReq{
  uint32 uid = 1;
  uint32 type = 2; // 通行证类型，2:进阶通行证 3:豪华通行证
  string token = 3;
}

message BuyBattlePassResp{
  uint32 getExtraExp = 1; // 额外获取的经验值
  repeated Reward rewards = 2; //额外奖励情况
  bool success = 3; // 是否购买成功
}

message BattlePassProcessReq {
  uint32 uid = 1;
  uint32 type = 2;  // 1:每日任务 2:每周任务 3:每期任务 4: 宝箱任务 5: 限时任务
}

message BattlePassProcessResp {
  message processItem{
    uint32 process = 1; // 进度
    uint32 total = 2; // 总进度
    uint32 isDone = 3; // 当天是否已完成，例如当天是否已签到
    string name = 4; // 任务名称
    uint32 exp = 5; //每次完成可获得的经验值
    repeated Stage stages = 6;    // 阶段任务详情
    uint32 startTime = 7;
    uint32 endTime = 8;
  }
  repeated processItem list = 1;
}

message Stage {
  uint32 value = 1;             // 阶段目标值
  uint32 exp = 2;   // 阶段奖励
  uint32 status = 3;            // 阶段状态（0-未完成，1-已完成）
}

message LotteryDataReq{
  uint32 uid = 1;
}

message LotteryDataResp {
  uint32 shell = 1; //贝壳数量
}

message LotteryReq {
  uint32 uid = 1;
  string token = 2;
  uint32 times = 3;  //抽奖次数
}

message LotteryResp {
  repeated Reward rewards = 1; //奖励
}

message LotteryRecordReq {
  uint32 uid = 1;
  uint32 page = 2;  //分页页码
  uint32 size = 3;  //分页大小 服务器固定10
}

message LotteryRecordResp {
  message Info {
    message Reward {
      string id = 1;          // 奖励ID
      uint32 num = 2;         // 奖励数量
      uint32 time = 3;        // 奖励天数
    }

    message ExtraParam {
      uint32 exchangeNum = 1; // 兑换个数
      uint32 lotteryType = 2; // 记录类型 1-抽奖 2兑换
    }

    repeated Reward reward = 1; // 奖励
    ExtraParam extraParam = 2; // 额外参数
    uint32 time = 3; // 获取时间
  }
  repeated Info records = 1;  // 记录
  uint32 total = 2;           // 总记录数
}

message mergeReq {
  uint32 uid = 1;
  string token = 2;
}

message mergeReqResp {
  bool success = 1; // 是否成功
}

message GetUserTbeanReq{
  uint32 uid = 1;
  string token = 2;
}

message GetUserTbeanResp{
  uint32 tbean = 1; // 用户t豆
}

message OpenTreasureBoxReq {
  uint32 uid = 1; // 用户ID
}

message OpenTreasureBoxResp {
  uint32 exp = 1; // 用户获取基础的经验值
  uint32 extraExp = 2; // 用户获取额外的经验值
}

message ExchangeRewardReq {
  uint32 uid = 1; // 用户ID
  string rewardId = 2; // 兑换奖励ID
  uint32 count = 3; // 兑换个数
}

message ExchangeRewardResp {
  bool success = 1; // 是否成功
  uint32 remainShellNum = 2; // 贝壳数量
}

message BuyLevelReq {
  uint32 uid = 1; // 用户ID
  string token = 2; // 支付token
  uint32 level = 3; // 购买等级数量
}

message BuyLevelResp {
}

/********** types **********/

message GuildInfo {
  string name = 1;
  uint32 guildId = 2; // 一般不外显
  uint32 displayId = 3;
}

message UserInfo {
  uint32 uid = 1;
  string username = 2;
  string alias = 3;
  string nickname = 4;
  uint32 sex = 5; // 非1为女
  optional GuildInfo guildInfo = 6; // 公会信息
  optional uint32 role = 7; // 用户角色，具体值由业务决定
}

enum ChannelStatus {
  LEAVE = 0; // 不在房
  STAY  = 1; // 在房
  WATCH = 2; // 看直播
  LIVE  = 3; // 直播中
  PK    = 4; // PK
}

message ChannelInfo {
  uint32 channelId = 1;
  ChannelStatus status = 2; // 在房状态
}

message MvpInfo {
  uint32 rank = 1;
  uint32 value = 2;
  string valueHuman = 3;
  UserInfo userInfo = 4;
}

message CommonRankItem {
  uint32 uid = 1;
  uint32 rank = 2;
  string rankHuman = 3;
  uint32 value = 4;
  string valueHuman = 5;
  uint32 ltPrevValue = 6;
  string ltPrevValueHuman = 7;
  uint32 gtNextValue = 8;
  string gtNextValueHuman = 9;
  UserInfo userInfo = 10;
  optional ChannelInfo channelInfo = 11; // 可选
  repeated MvpInfo mvpInfoList = 12; // 可选
}

  message Reward {
    string id = 1;  //奖励id
    uint32 num = 2;  //奖励个数
    uint32 time = 3;  //奖励天数
  }
