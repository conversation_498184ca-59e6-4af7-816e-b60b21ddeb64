// server/server.js - Mock server based on activity.proto
import { fileURLToPath } from 'node:url';
import { dirname, join } from 'node:path';
import process from 'node:process';
import express from 'express';
import cors from 'cors';
import morgan from 'morgan';
import dayjs from 'dayjs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const PORT = 3002;

// Middleware
app.use(cors());
app.use(express.json());
app.use(morgan('dev'));

// --- Helper Functions for Mock Data Generation ---
function generateUserInfo(uid) {
    return {
        uid,
        username: `tt110200509`,
        alias: `Alias_${uid}`,
        nickname: `Nickname ${uid}`,
        sex: uid % 2 === 0 ? 1 : 2,
        guildInfo: {
            name: `Guild_${String.fromCharCode(65 + (uid % 5))}`,
            guildId: 100 + (uid % 10),
            displayId: 1000 + (uid % 100),
        },
        role: uid % 3,
    };
}

function generateReward(id, num = 1, time = Math.floor(Date.now() / 1000)) {
    return {
        id,
        num,
        time,
    };
}

// 模拟用户数据存储
const userData = new Map();

// 初始化用户数据
function initUser(uid) {
    if (!userData.has(uid)) {
        userData.set(uid, {
            uid,
            exp: Math.floor(Math.random() * 5000),
            hasBuyBattlePass: Math.random() > 0.5,
            battlePassType: Math.floor(Math.random() * 3) + 1,
            hasBuyLevel: Math.floor(Math.random() * 10),
            hasOpenBox: Math.random() > 0.5 ? 1 : 0,
            shell: Math.floor(Math.random() * 1000) + 100,
            tbean: Math.floor(Math.random() * 10000) + 1000,
            receiveRecords: [],
            lotteryRecords: [],
        });
    }
    return userData.get(uid);
}

// --- Mock Data Definitions ---
const mockResponses = {
    // 初始化接口
    init: (reqBody) => {
        const user = initUser(reqBody.uid);
        return {
            serverTime: Math.floor(Date.now() / 1000),
            startTime: Math.floor(dayjs().subtract(7, 'day').valueOf() / 1000),
            endTime: Math.floor(dayjs().add(30, 'day').valueOf() / 1000),
            hasBuyBattlePass: user.hasBuyBattlePass,
            battlePassType: user.battlePassType,
            exp: user.exp,
            passStartDate: 20250428,
            hasBuyLevel: user.hasBuyLevel,
            everyLevelPrize: 100,
            buyLevelLimit: 60,
            everyLimitExp: 1000,
            openRisk: 1,
            advancedBattlePassPrice: 500,
            superBattlePassPrice: 1000,
            hasOpenBox: user.hasOpenBox,
            nextCanOpenTime: Math.floor(Date.now() / 1000) + 3600,
            openBoxExtraExp: 200,
            maxLevel: 60,
        };
    },

    // 升级通行证
    upgradeBattlePass: (reqBody) => {
        const user = initUser(reqBody.uid);
        user.hasBuyBattlePass = true;
        user.battlePassType = reqBody.type;

        return {
            getExtraExp: reqBody.type === 3 ? 500 : 200,
            rewards: [
                generateReward('battlepass_upgrade_reward', 1),
                generateReward('extra_exp_boost', 1),
            ],
            success: true,
        };
    },

    // 领取奖励
    receiveReward: (reqBody) => {
        const user = initUser(reqBody.uid);
        const rewards = [generateReward(`level_${reqBody.level}_reward`, 1)];

        // 记录领取状态
        user.receiveRecords.push({
            level: reqBody.level,
            type: user.battlePassType,
            time: Math.floor(Date.now() / 1000),
        });

        return { rewards };
    },

    // 一键领取
    receiveAll: (reqBody) => {
        const user = initUser(reqBody.uid);
        const currentLevel = Math.floor(user.exp / 1000) + 1;
        const rewards = [];

        // 模拟一键领取多个等级的奖励
        for (let level = 1; level <= Math.min(currentLevel, 10); level++) {
            rewards.push(generateReward(`level_${level}_reward`, 1));
        }

        return { rewards };
    },

    // 通行证任务进度
    battlePassTaskProcess: (reqBody) => {
        const taskTypes = {
            1: '每日任务',
            2: '每周任务',
            3: '每期任务',
            4: '宝箱任务',
            5: '限时任务',
        };

        const taskName = taskTypes[reqBody.type] || '未知任务';
        const currentTime = Math.floor(Date.now() / 1000);

        return {
            list: [
                {
                    process: Math.floor(Math.random() * 80) + 10,
                    total: 100,
                    isDone: Math.random() > 0.5 ? 1 : 0,
                    name: `${taskName}1`,
                    exp: 100,
                    stages: [
                        { value: 50, exp: 50, status: 1 },
                        { value: 100, exp: 100, status: 0 },
                    ],
                    startTime: currentTime - 86400,
                    endTime: currentTime + 86400,
                },
                {
                    process: Math.floor(Math.random() * 60) + 20,
                    total: 80,
                    isDone: Math.random() > 0.7 ? 1 : 0,
                    name: `${taskName}2`,
                    exp: 150,
                    stages: [
                        { value: 40, exp: 75, status: 1 },
                        { value: 80, exp: 150, status: 0 },
                    ],
                    startTime: currentTime - 86400,
                    endTime: currentTime + 86400,
                },
            ],
        };
    },

    // 通行证奖励领取记录
    battlePassReceiveRecord: (reqBody) => {
        const user = initUser(reqBody.uid);

        // 生成所有等级的奖励配置
        const generateLevelRewards = (prefix, levels = 60) => {
            return Array.from({ length: levels }, (_, i) => ({
                rewards: [generateReward(`${prefix}_level_${i + 1}`, 1)],
            }));
        };

        return {
            records: user.receiveRecords.map(r => ({
                level: r.level,
                type: r.type,
            })),
            allRewards: {
                normalCard: generateLevelRewards('normal'),
                advancedCard: generateLevelRewards('advanced'),
                superCard: generateLevelRewards('super'),
            },
        };
    },

    // 抽奖数据
    lotteryData: (reqBody) => {
        const user = initUser(reqBody.uid);
        return {
            shell: user.shell,
        };
    },

    // 抽奖
    lottery: (reqBody) => {
        const user = initUser(reqBody.uid);
        const { times = 1 } = reqBody;

        // 检查贝壳数量（假设每次抽奖消耗10个贝壳）
        const costPerLottery = 10;
        if (user.shell < times * costPerLottery) {
            throw new Error('贝壳数量不足');
        }

        // 扣除贝壳
        user.shell -= times * costPerLottery;

        // 生成奖励
        const rewards = Array.from({ length: times }, (_, i) =>
            generateReward(`lottery_reward_${Math.floor(Math.random() * 100)}`, 1));

        // 记录抽奖历史
        user.lotteryRecords.push({
            rewards,
            time: Math.floor(Date.now() / 1000),
            extraParam: {
                exchangeNum: times,
                lotteryType: 1,
            },
        });

        return { rewards };
    },

    // 抽奖记录
    lotteryRecord: (reqBody) => {
        const user = initUser(reqBody.uid);
        const { page = 1, size = 10 } = reqBody;

        // 生成模拟抽奖记录
        const records = Array.from({ length: size }, (_, i) => ({
            reward: [
                {
                    id: `lottery_reward_${i + 1}`,
                    num: 1,
                    time: Math.floor(Date.now() / 1000) - i * 3600,
                },
            ],
            extraParam: {
                exchangeNum: 1,
                lotteryType: 1,
            },
            time: Math.floor(Date.now() / 1000) - i * 3600,
        }));

        return {
            records,
            total: 100,
        };
    },

    // 获取用户T豆
    getUserTbean: (reqBody) => {
        const user = initUser(reqBody.uid);
        return {
            tbean: user.tbean,
        };
    },

    // 开启宝箱获取经验值
    openTreasureBox: (reqBody) => {
        const user = initUser(reqBody.uid);
        const baseExp = Math.floor(Math.random() * 100) + 50;
        const extraExp = user.battlePassType > 1 ? Math.floor(Math.random() * 50) + 25 : 0;

        // 增加用户经验
        user.exp += baseExp + extraExp;
        user.hasOpenBox = 1;

        return {
            exp: baseExp,
            extraExp,
        };
    },

    // 榜单
    giftRank: (reqBody) => {
        const { page = 1, size = 10, uid } = reqBody;
        const list = Array(size).fill(null).map((_, i) => ({
            uid: 10000 + i,
            rank: (page - 1) * size + i + 1,
            rankHuman: `${(page - 1) * size + i + 1}`,
            value: 10000 - i * 100,
            valueHuman: `${10000 - i * 100}`,
            ltPrevValue: 10000 - (i - 1) * 100,
            ltPrevValueHuman: `${10000 - (i - 1) * 100}`,
            gtNextValue: 10000 - (i + 1) * 100,
            gtNextValueHuman: `${10000 - (i + 1) * 100}`,
            userInfo: generateUserInfo(10000 + i),
        }));

        return {
            total: 100,
            list,
            self: uid
                ? {
                        uid,
                        rank: 50,
                        rankHuman: '50',
                        value: 5000,
                        valueHuman: '5000',
                        userInfo: generateUserInfo(uid),
                    }
                : null,
        };
    },

    // 荣耀堂
    gloryHall: (reqBody) => {
        const { page = 1, size = 10, uid } = reqBody;

        // 生成历史榜单数据
        const rankList = Array.from({ length: 3 }, (_, i) => ({
            startTime: Math.floor(Date.now() / 1000) - (i + 1) * 86400 * 7, // 每周一期
            endTime: Math.floor(Date.now() / 1000) - i * 86400 * 7,
            rankDetail: {
                total: 100,
                list: Array.from({ length: size }, (_, j) => ({
                    uid: 10000 + j,
                    rank: j + 1,
                    rankHuman: `${j + 1}`,
                    value: 10000 - j * 100,
                    valueHuman: `${10000 - j * 100}`,
                    ltPrevValue: j > 0 ? 10000 - (j - 1) * 100 : 0,
                    ltPrevValueHuman: j > 0 ? `${10000 - (j - 1) * 100}` : '0',
                    gtNextValue: 10000 - (j + 1) * 100,
                    gtNextValueHuman: `${10000 - (j + 1) * 100}`,
                    userInfo: generateUserInfo(10000 + j),
                })),
                self: uid
                    ? {
                            uid,
                            rank: 50,
                            rankHuman: '50',
                            value: 5000,
                            valueHuman: '5000',
                            userInfo: generateUserInfo(uid),
                        }
                    : null,
            },
        }));

        return { rankList };
    },

    // 兑换奖励
    exchangeReward: (reqBody) => {
        const user = initUser(reqBody.uid);
        const { rewardId, count = 1 } = reqBody;

        // 假设每个奖励需要50个贝壳
        const costPerReward = 50;
        const totalCost = count * costPerReward;

        if (user.shell < totalCost) {
            return {
                success: false,
                remainShellNum: user.shell,
            };
        }

        // 扣除贝壳
        user.shell -= totalCost;

        return {
            success: true,
            remainShellNum: user.shell,
        };
    },

    // 购买等级
    buyLevel: (reqBody) => {
        const user = initUser(reqBody.uid);
        const { level } = reqBody;

        // 增加用户购买的等级
        user.hasBuyLevel += level;
        user.exp += level * 1000; // 每等级1000经验

        return {};
    },

    // 移动端购买风控
    buyRiskControl: reqBody => ({
        errCode: 0,
        errMsg: 'success',
        errInfo: '',
    }),

    // 获取可完成任务的房间集合
    getCids: (reqBody) => {
        // 返回模拟的房间ID列表
        const cids = Array.from({ length: 10 }, (_, i) => 100000 + i);
        return { cids };
    },
};

// --- API Route Definitions ---
const apiEndpoints = {
    init: { method: 'post', reqType: 'InitReq', respType: 'init' },
    upgradeBattlePass: { method: 'post', reqType: 'UpgradeBattlePassReq', respType: 'upgradeBattlePass' },
    receiveReward: { method: 'post', reqType: 'ReceiveRewardReq', respType: 'receiveReward' },
    receiveAll: { method: 'post', reqType: 'ReceiveAllRewardReq', respType: 'receiveAll' },
    battlePassTaskProcess: { method: 'post', reqType: 'BattlePassProcessReq', respType: 'battlePassTaskProcess' },
    battlePassReceiveRecord: { method: 'post', reqType: 'BattlePassReceiveRecordReq', respType: 'battlePassReceiveRecord' },
    lotteryData: { method: 'post', reqType: 'LotteryDataReq', respType: 'lotteryData' },
    lottery: { method: 'post', reqType: 'LotteryReq', respType: 'lottery' },
    lotteryRecord: { method: 'post', reqType: 'LotteryRecordReq', respType: 'lotteryRecord' },
    getUserTbean: { method: 'post', reqType: 'GetUserTbeanReq', respType: 'getUserTbean' },
    buyRiskControl: { method: 'post', reqType: 'BuyRiskControlReq', respType: 'buyRiskControl' },
    openTreasureBox: { method: 'post', reqType: 'OpenTreasureBoxReq', respType: 'openTreasureBox' },
    giftRank: { method: 'post', reqType: 'GiftRankReq', respType: 'giftRank' },
    gloryHall: { method: 'post', reqType: 'GloryHallReq', respType: 'gloryHall' },
    exchangeReward: { method: 'post', reqType: 'ExchangeRewardReq', respType: 'exchangeReward' },
    buyLevel: { method: 'post', reqType: 'BuyLevelReq', respType: 'buyLevel' },
    getCids: { method: 'post', reqType: 'GetCidsReq', respType: 'getCids' },
};

// 请求参数验证规则
const validationRules = {
    InitReq: ['uid'],
    UpgradeBattlePassReq: ['uid', 'type'],
    ReceiveRewardReq: ['uid', 'level'],
    ReceiveAllRewardReq: ['uid'],
    BattlePassProcessReq: ['uid', 'type'],
    BattlePassReceiveRecordReq: ['uid'],
    LotteryDataReq: ['uid'],
    LotteryReq: ['uid', 'times'],
    LotteryRecordReq: ['uid', 'page', 'size'],
    GetUserTbeanReq: ['uid'],
    BuyRiskControlReq: ['uid', 'deviceId'],
    OpenTreasureBoxReq: ['uid'],
    GiftRankReq: ['uid', 'page', 'size'],
    GloryHallReq: ['uid', 'page', 'size'],
    ExchangeRewardReq: ['uid', 'rewardId', 'count'],
    BuyLevelReq: ['uid', 'level'],
    GetCidsReq: ['uid'],
};

// 验证请求参数
function validateRequest(reqBody, requiredFields = []) {
    const errors = [];
    if (!reqBody) {
        errors.push('Request body is required');
        return errors;
    }
    requiredFields.forEach((field) => {
        if (reqBody[field] === undefined || reqBody[field] === null) {
            errors.push(`Field '${field}' is required`);
        }
    });
    return errors;
}

// Register routes
Object.entries(apiEndpoints).forEach(([rpcName, config]) => {
    const routePath = `/activity.Activity/${rpcName}`;
    const responseGenerator = mockResponses[config.respType];

    if (!responseGenerator) {
        console.error(`No response generator found for ${config.respType} (RPC: ${rpcName})`);
        return;
    }

    app.post(routePath, (req, res) => {
        try {
            if (process.env.NODE_ENV === 'development') {
                console.log(`POST ${routePath} with body:`, req.body);
            }

            // 验证请求参数
            const requiredFields = validationRules[config.reqType] || [];
            const validationErrors = validateRequest(req.body, requiredFields);

            if (validationErrors.length > 0) {
                return res.status(400).json({
                    code: 400,
                    msg: `Validation failed: ${validationErrors.join(', ')}`,
                    data: null,
                });
            }

            const mockResponseData = responseGenerator(req.body);
            res.json({
                code: 0,
                msg: 'success',
                data: mockResponseData,
            });
        }
        catch (error) {
            console.error(`Error in POST ${routePath}:`, error);
            res.status(500).json({
                code: -1,
                msg: 'Internal server error',
                data: null,
            });
        }
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`Mock server is running on http://localhost:${PORT}`);
    console.log('Available RPC endpoints:');
    Object.entries(apiEndpoints).forEach(([rpcName, config]) => {
        console.log(`- ${config.method.toUpperCase()} /activity.Activity/${rpcName}`);
    });
});

export default app;
