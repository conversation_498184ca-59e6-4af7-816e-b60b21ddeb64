// server/server.js - Mock server based on activity.proto
import { fileURLToPath } from 'node:url';
import { dirname, join } from 'node:path';
import process from 'node:process';
import express from 'express';
import cors from 'cors';
import morgan from 'morgan';
import dayjs from 'dayjs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const PORT = 3002;

// Middleware
app.use(cors());
app.use(express.json());
app.use(morgan('dev'));

// --- Helper Functions for Mock Data Generation ---
function generateUserInfo(uid) {
    return {
        uid,
        username: `tt110200509`,
        alias: `Alias_${uid}`,
        nickname: `Nickname ${uid}`,
        sex: uid % 2 === 0 ? 1 : 2,
        guildInfo: {
            name: `Guild_${String.fromCharCode(65 + (uid % 5))}`,
            guildId: 100 + (uid % 10),
            displayId: 1000 + (uid % 100),
        },
        role: uid % 3,
    };
}

function generateReward(id, num = 1, time = Math.floor(Date.now() / 1000)) {
    return {
        id,
        num,
        time,
    };
}

// --- Mock Data Definitions ---
const mockResponses = {
    // 初始化接口
    init: reqBody => ({
        serverTime: Math.floor(Date.now() / 1000),
        startTime: Math.floor(dayjs().subtract(7, 'day').valueOf() / 1000),
        endTime: Math.floor(dayjs().add(7, 'day').valueOf() / 1000),
        hasBuyBattlePass: false,
        battlePassType: 0,
        exp: 0,
        passStartDate: 20250428,
        hasBuyLevel: 0,
        everyLevelPrize: 100,
        buyLevelLimit: 60,
    }),

    // 购买通行证
    buyBattlePass: reqBody => ({
        getetExtraExp: Math.floor(Math.random() * 1000),
        rewards: [
            generateReward('A1', 1),
            generateReward('A2', 2),
        ],
    }),

    // 升级通行证
    upgradeBattlePass: _reqBody => ({
        isGetExtraExp: Math.floor(Math.random() * 3) + 1,
        rewards: [generateReward('B1', 1)],
    }),

    // 领取奖励
    receiveReward: _reqBody => ({
        rewards: [generateReward('C1', 1)],
    }),

    // 一键领取
    receiveAll: _reqBody => ({
        rewards: [
            generateReward('D1', 1),
            generateReward('D2', 2),
        ],
    }),

    // 通行证任务进度
    battlePassTaskProcess: _reqBody => ({
        list: [
            {
                process: 50,
                total: 100,
                finishTimes: 1,
                name: '每日任务1',
                exp: 100,
            },
            {
                process: 25,
                total: 50,
                finishTimes: 2,
                name: '每日任务2',
                exp: 200,
            },
        ],
    }),

    // 通行证奖励领取记录
    battlePassReceiveRecord: _reqBody => ({
        normal: [
            { level: 1, id: 'N1', status: 1, num: 1, time: Math.floor(Date.now() / 1000) },
        ],
        advanced: [
            { level: 1, id: 'A1', status: 1, num: 1, time: Math.floor(Date.now() / 1000) },
        ],
        super: [
            { level: 1, id: 'S1', status: 1, num: 1, time: Math.floor(Date.now() / 1000) },
        ],
    }),

    // 抽奖数据
    lotteryData: _reqBody => ({
        ticket: Math.floor(Math.random() * 100),
        pieces: Math.floor(Math.random() * 1000),
    }),

    // 抽奖
    lottery: _reqBody => ({
        rewardId: `L${Math.floor(Math.random() * 100)}`,
    }),

    // 抽奖记录
    lotteryRecord: _reqBody => ({
        list: Array(10).fill(null).map((_, i) => ({
            rewardId: `L${i + 1}`,
            times: Math.floor(Date.now() / 1000) - i * 3600,
        })),
    }),

    // 获取用户t豆
    getUserTbean: _reqBody => ({
        tbean: Math.floor(Math.random() * 10000),
    }),

    // 开启宝箱获取经验值
    openTreasureBox: _reqBody => ({
        ext: Math.floor(Math.random() * 100) + 50,
    }),

    // 榜单
    giftRank: (reqBody) => {
        const { page = 1, size = 10, uid } = reqBody;
        const list = Array(size).fill(null).map((_, i) => ({
            uid: 10000 + i,
            rank: (page - 1) * size + i + 1,
            rankHuman: `${(page - 1) * size + i + 1}`,
            value: 10000 - i * 100,
            valueHuman: `${10000 - i * 100}`,
            ltPrevValue: 10000 - (i - 1) * 100,
            ltPrevValueHuman: `${10000 - (i - 1) * 100}`,
            gtNextValue: 10000 - (i + 1) * 100,
            gtNextValueHuman: `${10000 - (i + 1) * 100}`,
            userInfo: generateUserInfo(10000 + i),
        }));

        return {
            total: 100,
            list,
            self: uid
                ? {
                        uid,
                        rank: 50,
                        rankHuman: '50',
                        value: 5000,
                        valueHuman: '5000',
                        userInfo: generateUserInfo(uid),
                    }
                : null,
        };
    },

    // 兑换奖励
    exchangeReward: _reqBody => ({}),

    // 购买等级
    buyLevel: _reqBody => ({}),

    // 移动端购买风控
    buyRiskControl: _reqBody => ({
        errCode: 0,
        errMsg: 'success',
        errInfo: '',
    }),
};

// --- API Route Definitions ---
const apiEndpoints = {
    init: { method: 'post', reqType: 'InitReq', respType: 'init' },
    buyBattlePass: { method: 'post', reqType: 'BuyBattlePassReq', respType: 'buyBattlePass' },
    upgradeBattlePass: { method: 'post', reqType: 'UpgradeBattlePassReq', respType: 'upgradeBattlePass' },
    receiveReward: { method: 'post', reqType: 'ReceiveRewardReq', respType: 'receiveReward' },
    receiveAll: { method: 'post', reqType: 'ReceiveAllRewardReq', respType: 'receiveAll' },
    battlePassTaskProcess: { method: 'post', reqType: 'BattlePassProcessReq', respType: 'battlePassTaskProcess' },
    battlePassReceiveRecord: { method: 'post', reqType: 'BattlePassReceiveRecordReq', respType: 'battlePassReceiveRecord' },
    lotteryData: { method: 'post', reqType: 'LotteryDataReq', respType: 'lotteryData' },
    lottery: { method: 'post', reqType: 'LotteryReq', respType: 'lottery' },
    lotteryRecord: { method: 'post', reqType: 'LotteryRecordReq', respType: 'lotteryRecord' },
    getUserTbean: { method: 'post', reqType: 'GetUserTbeanReq', respType: 'getUserTbean' },
    buyRiskControl: { method: 'post', reqType: 'BuyRiskControlReq', respType: 'buyRiskControl' },
    openTreasureBox: { method: 'post', reqType: 'OpenTreasureBoxReq', respType: 'openTreasureBox' },
    giftRank: { method: 'post', reqType: 'GiftRankReq', respType: 'giftRank' },
    exchangeReward: { method: 'post', reqType: 'ExchangeRewardReq', respType: 'exchangeReward' },
    buyLevel: { method: 'post', reqType: 'BuyLevelReq', respType: 'buyLevel' },
};

// 请求参数验证规则
const validationRules = {
    InitReq: ['uid'],
    BuyBattlePassReq: ['uid', 'type'],
    UpgradeBattlePassReq: ['uid'],
    ReceiveRewardReq: ['uid', 'level'],
    ReceiveAllRewardReq: ['uid'],
    BattlePassProcessReq: ['uid', 'type'],
    BattlePassReceiveRecordReq: ['uid'],
    LotteryDataReq: ['uid'],
    LotteryReq: ['uid', 'times'],
    LotteryRecordReq: ['uid', 'page', 'size'],
    GetUserTbeanReq: ['uid'],
    BuyRiskControlReq: ['uid', 'deviceId'],
    OpenTreasureBoxReq: ['uid'],
    GiftRankReq: ['uid', 'page', 'size', 'type'],
    ExchangeRewardReq: ['uid', 'rewardId', 'count'],
    BuyLevelReq: ['uid', 'level'],
};

// 验证请求参数
function validateRequest(reqBody, requiredFields = []) {
    const errors = [];
    if (!reqBody) {
        errors.push('Request body is required');
        return errors;
    }
    requiredFields.forEach((field) => {
        if (reqBody[field] === undefined || reqBody[field] === null) {
            errors.push(`Field '${field}' is required`);
        }
    });
    return errors;
}

// Register routes
Object.entries(apiEndpoints).forEach(([rpcName, config]) => {
    const routePath = `/activity.Activity/${rpcName}`;
    const responseGenerator = mockResponses[config.respType];

    if (!responseGenerator) {
        console.error(`No response generator found for ${config.respType} (RPC: ${rpcName})`);
        return;
    }

    app.post(routePath, (req, res) => {
        try {
            if (process.env.NODE_ENV === 'development') {
                console.log(`POST ${routePath} with body:`, req.body);
            }

            // 验证请求参数
            const requiredFields = validationRules[config.reqType] || [];
            const validationErrors = validateRequest(req.body, requiredFields);

            if (validationErrors.length > 0) {
                return res.status(400).json({
                    code: 400,
                    msg: `Validation failed: ${validationErrors.join(', ')}`,
                    data: null,
                });
            }

            const mockResponseData = responseGenerator(req.body);
            res.json({
                code: 0,
                msg: 'success',
                data: mockResponseData,
            });
        }
        catch (error) {
            console.error(`Error in POST ${routePath}:`, error);
            res.status(500).json({
                code: -1,
                msg: 'Internal server error',
                data: null,
            });
        }
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`Mock server is running on http://localhost:${PORT}`);
    console.log('Available RPC endpoints:');
    Object.entries(apiEndpoints).forEach(([rpcName, config]) => {
        console.log(`- ${config.method.toUpperCase()} /activity.Activity/${rpcName}`);
    });
});

export default app;
